import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiSearch, FiExternalLink, FiDownload, FiLoader,
  FiTrendingUp, FiEye, FiHeart, FiUser, FiInfo,
  FiLink, FiRefreshCw, FiGlobe, FiTool, FiZap
} from 'react-icons/fi';
import WarehouseSearch from '../components/WarehouseSearch';
import axios from 'axios';

const ComponentName = memo((props) => {
  const [isWarehouseSearchOpen, setIsWarehouseSearchOpen] = useState(false);
  const [featuredModels, setFeaturedModels] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
  loadFeaturedModels();
  }, []);

  const loadFeaturedModels = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get('/api/download/warehouse/trending');
      if (response.data && response.data.data && response.data.data.models) {
        setFeaturedModels(response.data.data.models.slice(0, 6));
      } else {
        // Fallback to sample data if API fails
        setFeaturedModels([
          {
            _id: '1',
            title: 'Modern Living Room Set',
            author: 'SketchUp Team',
            thumbnail: '/images/placeholder.jpg',
            imageUrl: '/images/placeholder.jpg',
            modelUrl: 'https://3dwarehouse.sketchup.com/model/123',
            description: 'Complete modern living room furniture set',
            stats: { views: '12.5K', likes: '890' },
            category: 'Interior',
            downloads: 1250,
            views: 12500
          },
          {
            _id: '2',
            title: 'Kitchen Cabinet Collection',
            author: 'Interior Designer',
            thumbnail: '/images/placeholder.jpg',
            imageUrl: '/images/placeholder.jpg',
            modelUrl: 'https://3dwarehouse.sketchup.com/model/456',
            description: 'Professional kitchen cabinet designs',
            stats: { views: '8.2K', likes: '654' },
            category: 'Furniture',
            downloads: 820,
            views: 8200
          },
          {
            _id: '3',
            title: 'Office Furniture Pack',
            author: 'Workspace Pro',
            thumbnail: '/images/placeholder.jpg',
            imageUrl: '/images/placeholder.jpg',
            modelUrl: 'https://3dwarehouse.sketchup.com/model/789',
            description: 'Complete office furniture collection',
            stats: { views: '15.1K', likes: '1.2K' },
            category: 'Commercial',
            downloads: 1510,
            views: 15100
          }
        ]);
      }
    } catch (error) {
      // Use fallback data on error
      setFeaturedModels([
        {
          _id: '1',
          title: 'Modern Living Room Set',
          author: 'SketchUp Team',
          thumbnail: '/images/placeholder.jpg',
          imageUrl: '/images/placeholder.jpg',
          modelUrl: 'https://3dwarehouse.sketchup.com/model/123',
          description: 'Complete modern living room furniture set',
          stats: { views: '12.5K', likes: '890' },
          category: 'Interior',
          downloads: 1250,
          views: 12500
        },
        {
          _id: '2',
          title: 'Kitchen Cabinet Collection',
          author: 'Interior Designer',
          thumbnail: '/images/placeholder.jpg',
          imageUrl: '/images/placeholder.jpg',
          modelUrl: 'https://3dwarehouse.sketchup.com/model/456',
          description: 'Professional kitchen cabinet designs',
          stats: { views: '8.2K', likes: '654' },
          category: 'Furniture',
          downloads: 820,
          views: 8200
        },
        {
          _id: '3',
          title: 'Office Furniture Pack',
          author: 'Workspace Pro',
          thumbnail: '/images/placeholder.jpg',
          imageUrl: '/images/placeholder.jpg',
          modelUrl: 'https://3dwarehouse.sketchup.com/model/789',
          description: 'Complete office furniture collection',
          stats: { views: '15.1K', likes: '1.2K' },
          category: 'Commercial',
          downloads: 1510,
          views: 15100
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
    icon: <FiGlobe className="h-8 w-8" />,
      title: 'Global Library',
      description: 'Access millions of 3D models from Google\'s vast 3D Warehouse'
    },
    {
    icon: <FiDownload className="h-8 w-8" />,
      title: 'Smart Download',
      description: 'Download models through our proxy for better speed and reliability'
    },
    {
    icon: <FiLink className="h-8 w-8" />,
      title: 'Backup Links',
      description: 'Multiple download links ensure you always get your files'
    },
    {
    icon: <FiRefreshCw className="h-8 w-8" />,
      title: 'Auto Check',
      description: 'Automatically verify link status and find working alternatives'
    }
  ];

  const resources = [
    {
    title: 'Imported 3D Models',
      description: 'Models imported from Google 3D Warehouse into our library',
      icon: <FiGlobe className="h-6 w-6" />,
      action: () => setIsWarehouseSearchOpen(true)
    },
    {
    title: 'SketchUp Plugins',
      description: 'Essential plugins and tools for SketchUp available in our library',
      icon: <FiTool className="h-6 w-6" />,
      action: () => window.location.href = '/plugins'
    },
    {
    title: 'Smart Collections',
      description: 'AI-curated collections of related 3D models',
      icon: <FiZap className="h-6 w-6" />,
      action: () => window.location.href = '/collections'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">

      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white py-20">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-2xl animate-float"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-yellow-300 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-1/2 left-1/3 w-20 h-20 bg-pink-300 rounded-full blur-lg animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="container mx-auto px-4 text-center relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/30">
                🌐 3D Warehouse Integration
              </span>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-black mb-6 leading-tight">
                Kho 3D Toàn Cầu
                <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
                  Google Warehouse
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-4xl mx-auto leading-relaxed">
                Truy cập hàng triệu mô hình 3D từ Google 3D Warehouse với hệ thống tải xuống thông minh và đáng tin cậy
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
                <button
                  onClick={() => setIsWarehouseSearchOpen(true)}
                  className="group bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-2xl flex items-center justify-center gap-3"
                >
                  <FiSearch className="group-hover:scale-110 transition-transform" />
                  Tìm Kiếm 3D Warehouse
                  <FiExternalLink className="group-hover:translate-x-1 transition-transform" />
                </button>
                <button
                  onClick={loadFeaturedModels}
                  className="group bg-transparent border-2 border-white hover:bg-white/10 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 flex items-center justify-center gap-3"
                >
                  <FiRefreshCw className="group-hover:scale-110 transition-transform" />
                  Làm Mới Models
                </button>
              </div>

              {/* Quick Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
              >
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-black text-white mb-1">10M+</div>
                  <div className="text-white/80 text-sm">Models Available</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-black text-white mb-1">99%</div>
                  <div className="text-white/80 text-sm">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-black text-white mb-1">24/7</div>
                  <div className="text-white/80 text-sm">Availability</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-black text-white mb-1">Fast</div>
                  <div className="text-white/80 text-sm">Download Speed</div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Why Use Our Integration?
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Our smart download system enhances your 3D Warehouse experience with reliability and speed
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md"
                >
                  <div className="text-blue-600 dark:text-blue-400 mb-4 flex justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Models */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Featured Models
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Popular models from 3D Warehouse
                </p>
              </div>
              <button
                onClick={() => setIsWarehouseSearchOpen(true)}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <FiSearch className="h-4 w-4 mr-2" />
                Browse More
              </button>
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <FiLoader className="h-8 w-8 text-blue-600 mx-auto mb-4 animate-spin" />
                <p className="text-gray-600 dark:text-gray-400">Loading featured models...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredModels.map((model, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                  >
                    <img
                      src={model.thumbnail}
                      alt={model.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
  e.target.src = '/images/placeholder.jpg';
                      }}
                    />
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        {model.title}
                      </h3>
                      {model.author && (
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                          <FiUser className="h-4 w-4 mr-1" />
                          <span>{model.author}</span>
                        </div>
                      )}
                      {model.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                          {model.description}
                        </p>
                      )}
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          {model.stats?.views && (
                            <div className="flex items-center">
                              <FiEye className="h-3 w-3 mr-1" />
                              <span>{model.stats.views}</span>
                            </div>
                          )}
                          {model.stats?.likes && (
                            <div className="flex items-center">
                              <FiHeart className="h-3 w-3 mr-1" />
                              <span>{model.stats.likes}</span>
                            </div>
                          )}
                        </div>
                        <button
                          onClick={() => {
                            // Navigate to model detail page
                            window.location.href = `/model/${model._id || model.id}`;
                          }}
                          className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                          title="View Details"
                        >
                          <FiEye className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Resources Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Related Resources
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Explore these essential resources for SketchUp users
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {resources.map((resource, index) => (
                <motion.button
                  key={index}
                  onClick={resource.action}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="block p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all hover:scale-105 text-left w-full"
                >
                  <div className="flex items-center mb-4">
                    <div className="text-blue-600 dark:text-blue-400 mr-3">
                      {resource.icon}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {resource.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {resource.description}
                  </p>
                </motion.button>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-bold mb-4">
                Ready to Explore 3D Warehouse?
              </h2>
              <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
                Start searching and downloading 3D models with our enhanced integration
              </p>
              <button
                onClick={() => setIsWarehouseSearchOpen(true)}
                className="px-8 py-4 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
              >
                <FiSearch className="h-5 w-5 mr-2" />
                Start Searching Now
              </button>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Warehouse Search Modal */}
      {isWarehouseSearchOpen && (
        <WarehouseSearch
          onClose={() => setIsWarehouseSearchOpen(false)}
          onDownload={(model) => {
  toast.success(`Downloaded: ${model.title}`);
          }}
        />
      )}
    </div>
  );
};

export default Warehouse;
