import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiImage, FiDownload, FiStar, FiEye, FiChevronDown, FiChevronUp, FiTag } from 'react-icons/fi';

const ComponentName = memo((props) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showAllModels, setShowAllModels] = useState(false);

  if (!imageData || !analysis) return null;

  const { relatedModels = [], response, analysis: analysisDetails } = analysis;
  const displayModels = showAllModels ? relatedModels : relatedModels.slice(0, 3);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"
    >
      {/* Header */}
      <div className="flex items-start gap-3 mb-4">
        <div className="flex-shrink-0">
          <img
            src={imageData.preview}
            alt="Analyzed"
            className="w-16 h-16 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
          />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <FiImage className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Image Analysis Complete
            </span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {imageData.name} • {(imageData.size / 1024 / 1024).toFixed(1)} MB
          </p>
        </div>
      </div>

      {/* AI Response */}
      {response && (
        <div className="mb-4 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            {response}
          </p>
        </div>
      )}

      {/* Related Models */}
      {relatedModels.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Related 3D Models ({relatedModels.length})
          </h4>
          <div className="space-y-2">
            {displayModels.map((model, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg hover:bg-white dark:hover:bg-gray-800 transition-colors cursor-pointer"
              >
                <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg flex items-center justify-center">
                  <FiImage className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {model.title}
                  </h5>
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                    <span>{model.category}</span>
                    <div className="flex items-center gap-1">
                      <FiDownload className="h-3 w-3" />
                      <span>{model.downloads}</span>
                    </div>
                    {model.rating && (
                      <div className="flex items-center gap-1">
                        <FiStar className="h-3 w-3 text-yellow-500" />
                        <span>{model.rating}</span>
                      </div>
                    )}
                  </div>
                </div>
                {model.fileSize && (
                  <span className="text-xs text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                    {model.fileSize}MB
                  </span>
                )}
              </motion.div>
            ))}
          </div>

          {/* Show More/Less Button */}
          {relatedModels.length > 3 && (
            <button
              onClick={() => setShowAllModels(!showAllModels)}
              className="mt-2 flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
            >
              {showAllModels ? (
                <>
                  <FiChevronUp className="h-3 w-3" />
                  Show Less
                </>
              ) : (
                <>
                  <FiChevronDown className="h-3 w-3" />
                  Show {relatedModels.length - 3} More
                </>
              )}
            </button>
          )}
        </div>
      )}

      {/* Analysis Details Toggle */}
      {analysisDetails && (
        <div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          >
            {showDetails ? (
              <FiChevronUp className="h-3 w-3" />
            ) : (
              <FiChevronDown className="h-3 w-3" />
            )}
            Analysis Details
          </button>

          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg"
            >
              <div className="grid grid-cols-1 gap-3 text-xs">
                {analysisDetails.elements?.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Elements:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {analysisDetails.elements.map((element, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs"
                        >
                          {element}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {analysisDetails.objects?.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Objects:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {analysisDetails.objects.map((object, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded text-xs"
                        >
                          {object}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {analysisDetails.style && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Style:</span>
                    <span className="ml-2 px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-xs">
                      {analysisDetails.style}
                    </span>
                  </div>
                )}

                {analysisDetails.colors?.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Colors:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {analysisDetails.colors.map((color, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 rounded text-xs"
                        >
                          {color}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {analysisDetails.tags?.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Tags:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {analysisDetails.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs flex items-center gap-1"
                        >
                          <FiTag className="h-2 w-2" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ImageAnalysisResult;
