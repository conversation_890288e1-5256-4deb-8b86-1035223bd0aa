import React from 'react';
import { motion } from 'framer-motion';
import { FiFileText, FiShield, FiCreditCard, FiDownload, FiUpload, FiAlertCircle } from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import PageTransition from '../components/PageTransition';

const ComponentName = memo((props) => {
  // Last updated date
  const lastUpdated = 'January 15, 2023';

  // Terms sections
  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: <FiFileText />,
      content: `
        By accessing or using 3DSKETCHUP.NET ("the Service"), you agree to be bound by these Terms of Service ("Terms").
        If you disagree with any part of the terms, you may not access the Service.

        We reserve the right to update or change these Terms at any time without notice. Your continued use of the Service
        following the posting of any changes constitutes acceptance of those changes.
      `
    },
    {
      id: 'accounts',
      title: 'User Accounts',
      icon: <FiShield />,
      content: `
        When you create an account with us, you must provide accurate, complete, and current information. Failure to do so
        constitutes a breach of the Terms, which may result in immediate termination of your account.

        You are responsible for safeguarding the password that you use to access the Service and for any activities or actions
        under your password. We encourage you to use "strong" passwords (passwords that use a combination of upper and lower case
        letters, numbers, and symbols) with your account.

        You agree not to disclose your password to any third party. You must notify us immediately upon becoming aware of any
        breach of security or unauthorized use of your account.
      `
    },
    {
      id: 'content',
      title: 'Content Ownership and Licensing',
      icon: <FiUpload />,
      content: `
        3DSKETCHUP.NET contains user-generated content, and also may contain content owned or licensed by 3DSKETCHUP.NET
        ("Our Content"). Our Content is protected by copyright, trademark, patent, trade secret, and other laws, and we
        reserve all rights in Our Content.

        For user-generated content, you retain ownership of any intellectual property rights that you hold in that content.
        When you upload or share content, you grant us a worldwide, royalty-free license to use, host, store, reproduce,
        modify, create derivative works, communicate, publish, publicly perform, publicly display, and distribute that content.

        The license you grant us is for the limited purpose of operating, promoting, and improving our services, and to develop
        new ones. This license continues even if you stop using our services.

        You represent and warrant that you own or have the necessary rights to the content you post on or through the Service,
        and that the posting of your content does not violate the privacy rights, publicity rights, copyrights, contract rights,
        or any other rights of any person.
      `
    },
    {
      id: 'downloads',
      title: 'Model Downloads and Usage',
      icon: <FiDownload />,
      content: `
        When you download models from 3DSKETCHUP.NET, your rights to use these models depend on the specific license associated
        with each model. Models may be offered under different licenses, including:

        - Standard License: Allows personal and commercial use with restrictions.
        - Extended License: Allows broader commercial use, including resale in certain contexts.
        - Free License: Allows personal use only, with attribution required.

        You are responsible for reviewing and complying with the specific license terms for each model you download. Unauthorized
        use, reproduction, modification, distribution, or display of the models is strictly prohibited.

        Download limits may apply based on your subscription plan. Exceeding these limits may result in additional charges or
        temporary suspension of download privileges.
      `
    },
    {
      id: 'subscriptions',
      title: 'Subscriptions and Payments',
      icon: <FiCreditCard />,
      content: `
        Some aspects of the Service may be provided for a fee. You will be required to select a payment plan and provide accurate
        information regarding your payment method.

        By submitting such payment information, you automatically authorize us to charge all subscription fees incurred through
        your account to the payment method you provided.

        Subscription fees are billed in advance on a monthly or annual basis based on the type of subscription you select. Unless
        otherwise stated, subscriptions automatically renew at the end of each billing period.

        You may cancel your subscription at any time, but no refunds will be provided for any unused portion of the current
        billing period. After cancellation, you will continue to have access to the Service through the end of your current
        billing period.

        We reserve the right to change our subscription plans or adjust pricing for our service in any manner and at any time as
        we may determine in our sole discretion. Any price changes will take effect following notice to you.
      `
    },
    {
      id: 'prohibited',
      title: 'Prohibited Uses',
      icon: <FiAlertCircle />,
      content: `
        You agree not to use the Service:

        - In any way that violates any applicable federal, state, local, or international law or regulation.
        - To transmit, or procure the sending of, any advertising or promotional material, including any "junk mail," "chain letter,"
          "spam," or any other similar solicitation.
        - To impersonate or attempt to impersonate 3DSKETCHUP.NET, a 3DSKETCHUP.NET employee, another user, or any other person or entity.
        - To engage in any other conduct that restricts or inhibits anyone's use or enjoyment of the Service, or which may harm
          3DSKETCHUP.NET or users of the Service.
        - To upload or transmit viruses or any other type of malicious code that will or may be used in any way that will affect
          the functionality or operation of the Service.
        - To collect or track the personal information of others.
        - To damage, disable, overburden, or impair the Service or any server on which the Service is hosted.
      `
    },
    {
      id: 'termination',
      title: 'Termination',
      icon: <FiFileText />,
      content: `
        We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever,
        including without limitation if you breach the Terms.

        Upon termination, your right to use the Service will immediately cease. If you wish to terminate your account, you may
        simply discontinue using the Service or contact us to request account deletion.

        All provisions of the Terms which by their nature should survive termination shall survive termination, including, without
        limitation, ownership provisions, warranty disclaimers, indemnity, and limitations of liability.
      `
    }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />

      <PageTransition>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white py-24 overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-10 left-10 w-40 h-40 bg-white rounded-full blur-3xl animate-float"></div>
            <div className="absolute bottom-10 right-10 w-32 h-32 bg-yellow-300 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-pink-300 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="container mx-auto px-4 relative z-10 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/30">
                📋 Điều Khoản
              </span>
              <h1 className="text-5xl md:text-7xl font-black mb-8 leading-tight">
                <span className="bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  Điều Khoản Dịch Vụ
                </span>
              </h1>
              <p className="text-2xl md:text-3xl text-white/90 mb-8 max-w-4xl mx-auto leading-relaxed">
                Điều khoản và điều kiện sử dụng dịch vụ 3DSKETCHUP.NET
              </p>
              <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30">
                <span className="text-white/90 font-medium">Cập nhật lần cuối: {lastUpdated}</span>
              </div>
            </motion.div>
          </div>
        </section>

        <main className="flex-grow py-16 relative z-10">
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">

              {/* Introduction */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="glass-card rounded-3xl shadow-professional-lg p-8 mb-12 border border-white/20"
              >
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4">
                    <FiFileText className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-3xl font-black text-gray-900 dark:text-white">Giới Thiệu</h2>
                </div>
                <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                  Chào mừng bạn đến với 3DSKETCHUP.NET. Các Điều khoản Dịch vụ này ("Điều khoản") điều chỉnh việc sử dụng trang web,
                  sản phẩm và dịch vụ của chúng tôi. Vui lòng đọc kỹ các Điều khoản này trước khi sử dụng nền tảng của chúng tôi.
                  Bằng cách truy cập hoặc sử dụng 3DSKETCHUP.NET, bạn đồng ý bị ràng buộc bởi các Điều khoản này và Chính sách Bảo mật của chúng tôi.
                </p>
              </motion.div>

              {/* Table of Contents */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="glass-card rounded-3xl shadow-professional-lg p-8 mb-12 border border-white/20"
              >
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                    <FiFileText className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-3xl font-black text-gray-900 dark:text-white">Mục Lục</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {sections.map((section, index) => (
                    <motion.a
                      key={index}
                      href={`#${section.id}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                      className="glass-card p-4 rounded-2xl border border-white/10 hover:bg-white/20 transition-all duration-300 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                          <span className="text-white">{section.icon}</span>
                        </div>
                        <span className="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {section.title}
                        </span>
                      </div>
                    </motion.a>
                  ))}
                </div>
              </motion.div>

              {/* Terms Sections */}
              {sections.map((section, index) => (
                <motion.div
                  key={section.id}
                  id={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 + (index * 0.1) }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8"
                >
                  <div className="flex items-center mb-4">
                    <div className="text-blue-600 dark:text-blue-400 mr-3">
                      {section.icon}
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{section.title}</h2>
                  </div>
                  <div className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                    {section.content}
                  </div>
                </motion.div>
              ))}

              {/* Contact Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6"
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Contact Us</h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  If you have any questions about these Terms, please contact us at:
                </p>
                <div className="text-gray-700 dark:text-gray-300">
                  <p>Email: <EMAIL></p>
                  <p>Address: 123 Design Street, Suite 456, San Francisco, CA 94107, United States</p>
                </div>
              </motion.div>
            </div>
          </div>
        </main>
      </PageTransition>

      <Footer />
    </div>
  );
};

export default Terms;
