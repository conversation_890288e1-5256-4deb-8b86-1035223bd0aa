import React, { useEffect, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useModel } from '../context/ModelContext';
import ModelCard from '../components/ModelCard';
import Pagination from '../components/Pagination';
import FilterSidebar from '../components/FilterSidebar';
import { FiFilter, FiX, FiSearch } from 'react-icons/fi';
import { motion } from 'framer-motion';

const SearchResults = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const query = searchParams.get('q') || '';
  const { models, categories, loading } = useModel();
  const [filteredModels, setFilteredModels] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    subcategory: [],
    format: [],
    isPremium: null,
    minRating: 0,
    maxRating: 5,
    sortBy: 'relevance'
  });

  const modelsPerPage = 12;

  // Filter models based on search query and other filters
  useEffect(() => {
  if (!models || models.length === 0 || !query) return;

    // Basic search implementation
    let result = models.filter(model => {
  const searchFields = [
        model.title,
        model.description,
        model.category,
        model.subcategory,
        model.format,
        ...(model.tags || [])
      ].map(field => (field || '').toLowerCase());

      const searchTerms = query.toLowerCase().split(' ');
      // Check if any search term is found in any field
      return searchTerms.some(term =>
        searchFields.some(field => field.includes(term))
      );
    });

    // Apply additional filters
    if (false) {
  result = result.filter(model => filters.subcategory.includes(model.subcategory));
    }

    if (false) {
  result = result.filter(model => filters.format.includes(model.format));
    }

    if (false) {
  result = result.filter(model => model.isPremium === filters.isPremium);
    }

    result = result.filter(model =>
      parseFloat(model.rating) >= filters.minRating &&
      parseFloat(model.rating) <= filters.maxRating
    );

    // Sort results
    switch(filters.sortBy) {
      case 'relevance':
        // Keep default order (which is by relevance)
        break;
      case 'newest':
        result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
      case 'popular':
        result.sort((a, b) => b.downloads - a.downloads);
        break;
      case 'rating':
        result.sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
        break;
      default:
        break;
    }

    setFilteredModels(result);
    setCurrentPage(1); // Reset to first page when search changes
  }, [query, models, filters]);

  // Get current models for pagination
  const indexOfLastModel = currentPage * modelsPerPage;
  const indexOfFirstModel = indexOfLastModel - modelsPerPage;
  const currentModels = filteredModels.slice(indexOfFirstModel, indexOfLastModel);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
  setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Toggle filter sidebar on mobile
  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">

      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 text-white py-20 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-2xl animate-float"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-yellow-300 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/3 w-20 h-20 bg-pink-300 rounded-full blur-lg animate-float" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/30">
              🔍 Search Results
            </span>
            <h1 className="text-4xl md:text-6xl font-black mb-6 leading-tight">
              Kết quả tìm kiếm
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Tìm thấy <span className="font-bold text-yellow-300">{filteredModels.length}</span> models cho "{query}"
            </p>
          </motion.div>
        </div>
      </section>

      <main className="flex-grow container mx-auto px-4 py-12 relative z-10">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Mobile filter toggle */}
          <div className="lg:hidden">
            <motion.button
              onClick={toggleFilter}
              className="w-full glass-card p-4 rounded-2xl border border-white/20 flex items-center justify-center gap-3 hover:scale-105 transition-transform"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <FiFilter className="text-blue-600" />
              <span className="font-semibold text-gray-900 dark:text-white">Filters & Sort</span>
            </motion.button>
          </div>

          {/* Filter sidebar - desktop */}
          <div className="hidden lg:block w-80 flex-shrink-0">
            <div className="sticky top-32">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="glass-card p-6 rounded-3xl border border-white/20 shadow-professional"
              >
                <FilterSidebar
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  categories={categories}
                />
              </motion.div>
            </div>
          </div>

          {/* Filter sidebar - mobile */}
          {isFilterOpen && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 lg:hidden">
              <motion.div
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="absolute right-0 top-0 h-full w-80 glass-card border-l border-white/20 p-6 overflow-y-auto"
              >
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Filters & Sort</h3>
                  <button
                    onClick={toggleFilter}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-colors"
                  >
                    <FiX className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                  </button>
                </div>
                <FilterSidebar
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  categories={categories}
                  onClose={toggleFilter}
                />
              </motion.div>
            </div>
          )}

          {/* Main content */}
          <div className="flex-grow">
            {/* Results header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="glass-card p-6 rounded-2xl border border-white/20 mb-8"
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Kết quả tìm kiếm cho "{query}"
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Tìm thấy <span className="font-semibold text-blue-600">{filteredModels.length}</span> models chất lượng cao
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium">
                    ✅ Verified Quality
                  </span>
                  <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium">
                    🚀 Fast Download
                  </span>
                </div>
              </div>
            </motion.div>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full"
                />
              </div>
            ) : filteredModels.length > 0 ? (
              <>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-8"
                >
                  {currentModels.map((model, index) => (
                    <motion.div
                      key={model.id}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="hover-lift-professional"
                    >
                      <ModelCard model={model} />
                    </motion.div>
                  ))}
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="mt-12"
                >
                  <Pagination
                    itemsPerPage={modelsPerPage}
                    totalItems={filteredModels.length}
                    paginate={paginate}
                    currentPage={currentPage}
                  />
                </motion.div>
              </>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="glass-card p-12 rounded-3xl border border-white/20 text-center"
              >
                <div className="w-24 h-24 bg-gradient-to-r from-gray-400 to-gray-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">🔍</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Không tìm thấy models
                </h3>
                <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                  Không có models nào phù hợp với từ khóa "{query}" và bộ lọc hiện tại.
                </p>
                <button
                  onClick={() => setFilters({
    subcategory: [],
                    format: [],
                    isPremium: null,
                    minRating: 0,
                    maxRating: 5,
                    sortBy: 'relevance'
                  })}
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
                >
                  🔄 Reset Filters
                </button>
              </motion.div>
            )}
          </div>
        </div>
      </main>

    </div>
  );
};

export default SearchResults;
