import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FiDownload,
  FiStar,
  FiUsers,
  FiTrendingUp,
  FiArrowRight,
  FiPlay,
  FiEye,
  FiHeart
} from 'react-icons/fi';
import { useModel } from '../context/ModelContext';
import ModelCard from '../components/ModelCard';
import LoadingSpinner from '../components/LoadingSpinner';
import StatisticsDisplay from '../components/StatisticsDisplay';
import PopularModels from '../components/PopularModels';
import RecentModels from '../components/RecentModels';
import AnimatedModelCounter from '../components/AnimatedModelCounter';
import FloatingModelPreviews from '../components/FloatingModelPreviews';
import realDataService from '../services/realDataService';

const Home = () => {
  const { models, loading, error, fetchModels } = useModel();
  const [featuredModels, setFeaturedModels] = useState([]);
  const [popularModels, setPopularModels] = useState([]);
  const [recentModels, setRecentModels] = useState([]);
  const [stats, setStats] = useState({
    totalModels: 0,
    totalDownloads: 0,
    totalUsers: 0,
    categories: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  // Fetch homepage data using real API calls
  useEffect(() => {
    const fetchHomepageData = async () => {
      setIsLoading(true);
      try {
        // Fetch all required data in parallel
        const [
          allModelsData,
          featuredData,
          popularData,
          recentData,
          statsData
        ] = await Promise.all([
          realDataService.getAllModels(),
          realDataService.getFeaturedModels(),
          realDataService.getPopularModels(),
          realDataService.getRecentModels(),
          realDataService.getStats()
        ]);

        // Set the data
        setFeaturedModels(featuredData.slice(0, 6));
        setPopularModels(popularData.slice(0, 8));
        setRecentModels(recentData.slice(0, 8));
        setStats(statsData);

      } catch (error) {
        console.error('Error fetching homepage data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHomepageData();
  }, []);

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-2xl animate-float"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-yellow-300 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/3 w-20 h-20 bg-pink-300 rounded-full blur-lg animate-float" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="container mx-auto px-4 py-20 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/30">
              🚀 Premium 3D Model Repository
            </span>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-black mb-6 leading-tight">
              Khám Phá Thế Giới
              <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
                Mô Hình 3D
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-4xl mx-auto leading-relaxed">
              Bộ sưu tập mô hình 3D chất lượng cao cho SketchUp, Blender và các phần mềm thiết kế khác.
              Tải xuống miễn phí và khám phá không giới hạn.
            </p>

            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
              <Link
                to="/warehouse"
                className="group bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-2xl flex items-center justify-center gap-3"
              >
                <FiEye className="group-hover:scale-110 transition-transform" />
                Khám Phá Models
                <FiArrowRight className="group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                to="/features-demo"
                className="group bg-transparent border-2 border-white hover:bg-white/10 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 flex items-center justify-center gap-3"
              >
                <FiPlay className="group-hover:scale-110 transition-transform" />
                Xem Demo
              </Link>
            </div>

            {/* Statistics */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
            >
              <div className="text-center">
                <AnimatedModelCounter end={stats.totalModels} duration={2000} />
                <div className="text-white/80 text-sm mt-1">Models</div>
              </div>
              <div className="text-center">
                <AnimatedModelCounter end={stats.totalDownloads} duration={2500} />
                <div className="text-white/80 text-sm mt-1">Downloads</div>
              </div>
              <div className="text-center">
                <AnimatedModelCounter end={stats.totalUsers} duration={2200} />
                <div className="text-white/80 text-sm mt-1">Users</div>
              </div>
              <div className="text-center">
                <AnimatedModelCounter end={stats.categories} duration={1800} />
                <div className="text-white/80 text-sm mt-1">Categories</div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Floating Model Previews */}
        <FloatingModelPreviews models={featuredModels.slice(0, 3)} />
      </section>

      {/* Featured Models Section */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium mb-4">
              ⭐ Featured Collection
            </span>
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-6">
              Mô Hình Nổi Bật
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Khám phá những mô hình 3D chất lượng cao được tuyển chọn kỹ lưỡng bởi cộng đồng
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {featuredModels.map((model, index) => (
              <motion.div
                key={model.id}
                variants={fadeInUp}
                className="hover-lift-professional"
              >
                <ModelCard model={model} featured={true} />
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <Link
              to="/warehouse"
              className="group inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
            >
              <FiEye className="group-hover:scale-110 transition-transform" />
              Xem Tất Cả Models
              <FiArrowRight className="group-hover:translate-x-1 transition-transform" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Popular Models Section */}
      <section className="py-20 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <span className="inline-block px-4 py-2 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm font-medium mb-4">
              🔥 Trending Now
            </span>
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-6">
              Phổ Biến Nhất
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Những mô hình được tải xuống nhiều nhất và được yêu thích bởi cộng đồng
            </p>
          </motion.div>

          <PopularModels models={popularModels} />
        </div>
      </section>

      {/* Recent Models Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <span className="inline-block px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full text-sm font-medium mb-4">
              🆕 Latest Updates
            </span>
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-6">
              Mới Nhất
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Khám phá những mô hình mới nhất được thêm vào bộ sưu tập của chúng tôi
            </p>
          </motion.div>

          <RecentModels models={recentModels} />
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
        <div className="container mx-auto px-4">
          <StatisticsDisplay stats={stats} />
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-black mb-6">
              Sẵn Sàng Bắt Đầu?
            </h2>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Tham gia cộng đồng hàng nghìn nhà thiết kế và kiến trúc sư đang sử dụng 3DSKETCHUP.NET
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/register"
                className="group bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-2xl flex items-center justify-center gap-3"
              >
                <FiUsers className="group-hover:scale-110 transition-transform" />
                Đăng Ký Miễn Phí
                <FiArrowRight className="group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                to="/warehouse"
                className="group bg-transparent border-2 border-white hover:bg-white/10 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 flex items-center justify-center gap-3"
              >
                <FiDownload className="group-hover:scale-110 transition-transform" />
                Tải Xuống Ngay
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
