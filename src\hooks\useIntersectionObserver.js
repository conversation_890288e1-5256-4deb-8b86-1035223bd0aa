import React, { useState, useEffect, useRef } from 'react';

/**
 * Custom hook for detecting when an element enters the viewport
 *
 * @param {Object} options - Intersection Observer options
 * @param {string} options.rootMargin - Margin around the root element
 * @param {number|number[]} options.threshold - Threshold(s) at which to trigger callback
 * @param {boolean} options.triggerOnce - Whether to unobserve after first intersection
 * @param {Element} options.root - The element that is used as the viewport
 * @returns {[React.RefObject, boolean, IntersectionObserverEntry]} - [ref to observe, isIntersecting, entry]
 */
const ComponentName = memo((props) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState(null);
  const elementRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    // Cleanup previous observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);

        // Unobserve after first intersection if triggerOnce is true
        if (entry.isIntersecting && triggerOnce && elementRef.current) {
          observerRef.current.unobserve(elementRef.current);
        }
      },
      { rootMargin, threshold, root }
    );

    // Start observing
    const currentElement = elementRef.current;
    if (currentElement) {
      observerRef.current.observe(currentElement);
    }

    // Cleanup on unmount
    return () => {
      if (observerRef.current && currentElement) {
        observerRef.current.unobserve(currentElement);
        observerRef.current.disconnect();
      }
    };
  }, [rootMargin, threshold, triggerOnce, root]);

  return [elementRef, isIntersecting, entry];
};

export default useIntersectionObserver;
