import React, { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for optimized image loading
 *
 * @param {Object} options - Configuration options
 * @param {boolean} options.enableWebP - Whether to use WebP format when supported
 * @param {boolean} options.enableLazyLoading - Whether to enable lazy loading
 * @param {boolean} options.enableProgressiveLoading - Whether to enable progressive loading
 * @param {boolean} options.enableCaching - Whether to enable caching
 * @param {number} options.cacheDuration - Cache duration in milliseconds
 * @returns {Object} Optimized image utilities
 */
const ComponentName = memo((props) => {
  const [supportsWebP, setSupportsWebP] = useState(false);
  const [imageCache, setImageCache] = useState({});
  const [loadedImages, setLoadedImages] = useState({});

  // Check WebP support
  useEffect(() => {
    if (!enableWebP) return;

    const checkWebPSupport = async () => {
      try {
        const webpSupported = document.createElement('canvas')
          .toDataURL('image/webp')
          .indexOf('data:image/webp') === 0;
        setSupportsWebP(webpSupported);
      } catch (e) {
        setSupportsWebP(false);
      }
    };

    checkWebPSupport();
  }, [enableWebP]);

  // Clear expired cache items
  useEffect(() => {
    if (!enableCaching) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const newCache = { ...imageCache };
      let hasChanges = false;

      Object.keys(newCache).forEach(key => {
        if (now - newCache[key].timestamp > cacheDuration) {
          delete newCache[key];
          hasChanges = true;
        }
      });

      if (hasChanges) {
        setImageCache(newCache);
      }
    }, 60 * 60 * 1000); // Check every hour

    return () => clearInterval(interval);
  }, [imageCache, cacheDuration, enableCaching]);

  // Get optimized image URL
  const getOptimizedImageUrl = useCallback((url, options = {}) => {
    if (!url) return url;

    const {
      width,
      height,
      quality = 80,
      format = 'auto'
    } = options;

    // If URL is already a data URL or blob URL, return as is
    if (url.startsWith('data:') || url.startsWith('blob:')) {
      return url;
    }

    // If URL is a relative path, make it absolute
    let absoluteUrl = url;
    if (url.startsWith('/')) {
      absoluteUrl = `${window.location.origin}${url}`;
    }

    // If WebP is supported and enabled, use WebP format
    if (enableWebP && supportsWebP && format === 'auto') {
      // If URL already has a query string
      if (absoluteUrl.includes('?')) {
        return `${absoluteUrl}&format=webp&q=${quality}${width ? `&w=${width}` : '}${height ? `&h=${height}` : '}`;
      } else {
        return `${absoluteUrl}?format=webp&q=${quality}${width ? `&w=${width}` : '}${height ? `&h=${height}` : '}`;
      }
    }

    // If specific format is requested
    if (format !== 'auto') {
      // If URL already has a query string
      if (absoluteUrl.includes('?')) {
        return `${absoluteUrl}&format=${format}&q=${quality}${width ? `&w=${width}` : '}${height ? `&h=${height}` : '}`;
      } else {
        return `${absoluteUrl}?format=${format}&q=${quality}${width ? `&w=${width}` : '}${height ? `&h=${height}` : '}`;
      }
    }

    // If no format change, but resize or quality change
    if (width || height || quality !== 80) {
      // If URL already has a query string
      if (absoluteUrl.includes('?')) {
        return `${absoluteUrl}&q=${quality}${width ? `&w=${width}` : '}${height ? `&h=${height}` : '}`;
      } else {
        return `${absoluteUrl}?q=${quality}${width ? `&w=${width}` : '}${height ? `&h=${height}` : '}`;
      }
    }

    return absoluteUrl;
  }, [enableWebP, supportsWebP]);

  // Preload image
  const preloadImage = useCallback((url, options = {}) => {
    if (!url) return Promise.reject(new Error('No URL provided'));

    const optimizedUrl = getOptimizedImageUrl(url, options);

    // Check if image is already loaded
    if (loadedImages[optimizedUrl]) {
      return Promise.resolve(optimizedUrl);
    }

    // Check if image is in cache
    if (enableCaching && imageCache[optimizedUrl]) {
      const now = Date.now();
      if (now - imageCache[optimizedUrl].timestamp <= cacheDuration) {
        setLoadedImages(prev => ({
          ...prev,
          [optimizedUrl]: true
        }));
        return Promise.resolve(optimizedUrl);
      }
    }

    // Load image
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        // Cache image
        if (enableCaching) {
          setImageCache(prev => ({
            ...prev,
            [optimizedUrl]: {
              timestamp: Date.now()
            }
          }));
        }

        // Mark as loaded
        setLoadedImages(prev => ({
          ...prev,
          [optimizedUrl]: true
        }));

        resolve(optimizedUrl);
      };

      img.onerror = () => {
        reject(new Error(`Failed to load image: ${optimizedUrl}`));
      };

      img.src = optimizedUrl;
    });
  }, [getOptimizedImageUrl, loadedImages, imageCache, enableCaching, cacheDuration]);

  // Get progressive image sources
  const getProgressiveImageSources = useCallback((url, options = {}) => {
    if (!url || !enableProgressiveLoading) {
      return {
        placeholder: url,
        thumbnail: url,
        small: url,
        medium: url,
        large: url,
        original: url
      };
    }

    const {
      placeholderWidth = 10,
      thumbnailWidth = 100,
      smallWidth = 300,
      mediumWidth = 600,
      largeWidth = 1200
    } = options;

    return {
      placeholder: getOptimizedImageUrl(url, { width: placeholderWidth, quality: 30 }),
      thumbnail: getOptimizedImageUrl(url, { width: thumbnailWidth, quality: 50 }),
      small: getOptimizedImageUrl(url, { width: smallWidth, quality: 70 }),
      medium: getOptimizedImageUrl(url, { width: mediumWidth, quality: 80 }),
      large: getOptimizedImageUrl(url, { width: largeWidth, quality: 90 }),
      original: url
    };
  }, [enableProgressiveLoading, getOptimizedImageUrl]);

  // Clear image cache
  const clearImageCache = useCallback(() => {
    setImageCache({});
    setLoadedImages({});
  }, []);

  return {
    getOptimizedImageUrl,
    preloadImage,
    getProgressiveImageSources,
    clearImageCache,
    supportsWebP,
    loadedImages
  };
};

export default useOptimizedImages;
