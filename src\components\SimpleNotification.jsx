import React, { useState } from 'react';
import { FiInfo, FiX } from 'react-icons/fi';

const ComponentName = memo((props) => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'warning':
        return <FiInfo className="h-5 w-5 text-yellow-400" />;
      case 'error':
        return <FiInfo className="h-5 w-5 text-red-400" />;
      case 'success':
        return <FiInfo className="h-5 w-5 text-green-400" />;
      default:
        return <FiInfo className="h-5 w-5 text-blue-400" />;
    }
  };

  return (
    <div className={`fixed bottom-4 right-4 max-w-md border rounded-lg shadow-lg z-50 ${getTypeStyles()}`}>
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium">
              {message}
            </p>
          </div>
          {dismissible && (
            <div className="ml-auto pl-3">
              <button
                onClick={() => setIsVisible(false)}
                className="inline-flex text-gray-400 hover:text-gray-500 transition-colors"
              >
                <FiX className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleNotification;
