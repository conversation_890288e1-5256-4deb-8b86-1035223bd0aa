import React, { useState, useEffect } from 'react';

/**
 * Custom hook for preloading images
 *
 * @param {string|string[]} sources - Image URL(s) to preload
 * @param {Object} options - Options
 * @param {boolean} options.sequential - Whether to load images sequentially
 * @param {number} options.timeout - Timeout in ms before considering an image failed to load
 * @returns {Object} - { loaded, progress, errors }
 */
const ComponentName = memo((props) => {
  const [loaded, setLoaded] = useState(false);
  const [progress, setProgress] = useState(0);
  const [errors, setErrors] = useState([]);

  useEffect(() => {
    if (!sources || (Array.isArray(sources) && sources.length === 0)) {
      setLoaded(true);
      setProgress(100);
      return;
    }

    const imageSources = Array.isArray(sources) ? sources : [sources];
    const totalImages = imageSources.length;
    let loadedCount = 0;
    const errorList = [];

    // Reset state when sources change
    setLoaded(false);
    setProgress(0);
    setErrors([]);

    // Function to load a single image
    const loadImage = (src) => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        let timedOut = false;

        // Set timeout
        const timeoutId = setTimeout(() => {
          timedOut = true;
          reject(new Error(`Image load timed out: ${src}`));
        }, timeout);

        img.onload = () => {
          if (!timedOut) {
            clearTimeout(timeoutId);
            resolve(src);
          }
        };

        img.onerror = () => {
          if (!timedOut) {
            clearTimeout(timeoutId);
            reject(new Error(`Failed to load image: ${src}`));
          }
        };

        img.src = src;

        // If image is already cached, onload may have been called before we set the handlers
        if (img.complete) {
          clearTimeout(timeoutId);
          resolve(src);
        }
      });
    };

    // Load images sequentially
    const loadSequentially = async () => {
      for (const src of imageSources) {
        try {
          await loadImage(src);
          loadedCount++;
          setProgress(Math.round((loadedCount / totalImages) * 100));
        } catch (error) {
          errorList.push({ src, error: error.message });
          setErrors([...errorList]);
        }
      }

      setLoaded(true);
    };

    // Load images in parallel
    const loadInParallel = () => {
      const promises = imageSources.map(src =>
        loadImage(src)
          .then(() => {
            loadedCount++;
            setProgress(Math.round((loadedCount / totalImages) * 100));
          })
          .catch(error => {
            errorList.push({ src, error: error.message });
            setErrors([...errorList]);
          })
      );

      Promise.all(promises).then(() => {
        setLoaded(true);
      });
    };

    // Start loading
    if (sequential) {
      loadSequentially();
    } else {
      loadInParallel();
    }

    // No cleanup needed
  }, [sources, sequential, timeout]);

  return { loaded, progress, errors };
};

export default useImagePreload;
