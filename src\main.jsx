import React from 'react'
import ReactDOM from 'react-dom/client'

// Assign React to window for global access
// This must happen before any other imports that might use React
window.React = React;
window.ReactDOM = ReactDOM;

// Verify React is properly assigned to window
if (!window.React) {
  window.React = React;
}

// Disable React DevTools warning in production
if (import.meta.env.PROD) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = {
    isDisabled: true,
    supportsFiber: true,
    inject: () => {},
    onCommitFiberRoot: () => {},
    onCommitFiberUnmount: () => {},
  };
}

// Add error handling for React hooks
// Note: We can't directly override imported hooks, so we'll use a different approach
// Instead, we'll add a global error handler that catches React errors

// Only import other modules after React is assigned to window
import './index.css'
import './slick-carousel.css'
import { setupGlobalErrorHandler } from './utils/errorHandling'

// Import App after React is assigned to window
import App from './App.jsx'

// Set up global error handler
setupGlobalErrorHandler();

// Preload critical resources if needed
// import { preloadResources } from './utils/performance'
// preloadResources(['/assets/critical-image.jpg', '/assets/fonts/main-font.woff2']);

// Create root with error handling
const rootElement = document.getElementById('root');
if (!rootElement) {
  } else {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
}
