import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, Video, VideoOff, Mic, MicOff, Share2, 
  MessageCircle, Hand, Cursor, Eye, Settings,
  Monitor, Phone, PhoneOff, Camera, CameraOff,
  Screen, Maximize, Minimize, Volume2, VolumeX
} from 'lucide-react';

const ComponentName = memo((props) => {
  const [isCollaborating, setIsCollaborating] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [isVideoEnabled, setIsVideoEnabled] = useState(false);
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [cursorPositions, setCursorPositions] = useState({});
  const [annotations, setAnnotations] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingTool, setDrawingTool] = useState('pen');
  const [collaborationMode, setCollaborationMode] = useState('view'); // view, edit, present
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  const peerConnectionsRef = useRef({});

  // Collaboration modes
  const modes = {
    view: { name: 'Xem', icon: Eye, color: 'blue' },
    edit: { name: 'Chỉnh sửa', icon: Settings, color: 'green' },
    present: { name: 'Trình bày', icon: Monitor, color: 'purple' }
  };

  // Drawing tools
  const drawingTools = [
    { id: 'pen', name: 'Bút', icon: '✏️' },
    { id: 'highlighter', name: 'Bút dạ quang', icon: '🖍️' },
    { id: 'arrow', name: 'Mũi tên', icon: '➡️' },
    { id: 'circle', name: 'Vòng tròn', icon: '⭕' },
    { id: 'rectangle', name: 'Hình chữ nhật', icon: '⬜' },
    { id: 'text', name: 'Văn bản', icon: '📝' }
  ];

  useEffect(() => {
    if (isCollaborating) {
      initializeCollaboration();
    }
    
    return () => {
      cleanup();
    };
  }, [isCollaborating]);

  const initializeCollaboration = async () => {
    try {
      // Initialize WebRTC connections
      await setupMediaDevices();
      
      // Connect to collaboration server
      connectToCollaborationServer();
      
      // Setup real-time cursor tracking
      setupCursorTracking();
      
      // Initialize drawing canvas
      setupDrawingCanvas();
      
    } catch (error) {
      }
  };

  const setupMediaDevices = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      }
  };

  const connectToCollaborationServer = () => {
    // WebSocket connection for real-time collaboration
    const ws = new WebSocket(`ws://localhost:5002/collaborate/${modelId}`);
    
    ws.onopen = () => {
      };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleCollaborationMessage(data);
    };
    
    ws.onclose = () => {
      };
  };

  const handleCollaborationMessage = useCallback((data) => {
    switch (data.type) {
      case 'participant-joined':
        setParticipants(prev => [...prev, data.participant]);
        break;
      case 'participant-left':
        setParticipants(prev => prev.filter(p => p.id !== data.participantId));
        break;
      case 'cursor-move':
        setCursorPositions(prev => ({
          ...prev,
          [data.userId]: data.position
        }));
        break;
      case 'annotation-added':
        setAnnotations(prev => [...prev, data.annotation]);
        break;
      case 'chat-message':
        setChatMessages(prev => [...prev, data.message]);
        break;
      case 'mode-changed':
        setCollaborationMode(data.mode);
        break;
    }
  };

  const setupCursorTracking = () => {
    const handleMouseMove = useCallback((e) => {
      const position = {
        x: e.clientX,
        y: e.clientY,
        timestamp: Date.now()
      };
      
      // Send cursor position to other participants
      broadcastCursorPosition(position);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  };

  const setupDrawingCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    const handleMouseDown = useCallback((e) => {
      if (collaborationMode !== 'edit') return;
      
      setIsDrawing(true);
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      ctx.beginPath();
      ctx.moveTo(x, y);
    };
    
    const handleMouseMove = useCallback((e) => {
      if (!isDrawing || collaborationMode !== 'edit') return;
      
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      ctx.lineTo(x, y);
      ctx.stroke();
      
      // Broadcast drawing data
      broadcastDrawing({ x, y, tool: drawingTool });
    };
    
    const handleMouseUp = useCallback(() => {
      setIsDrawing(false);
    };
    
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    
    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
    };
  };

  const broadcastCursorPosition = (position) => {
    // Send to collaboration server
    };

  const broadcastDrawing = (drawingData) => {
    // Send drawing data to collaboration server
    };

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !isVideoEnabled;
      }
    }
  };

  const toggleAudio = () => {
    setIsAudioEnabled(!isAudioEnabled);
    if (streamRef.current) {
      const audioTrack = streamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !isAudioEnabled;
      }
    }
  };

  const startScreenShare = async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });
      
      setIsScreenSharing(true);
      // Replace video track with screen share
      
    } catch (error) {
      }
  };

  const stopScreenShare = () => {
    setIsScreenSharing(false);
    // Restore camera video
  };

  const sendChatMessage = () => {
    if (!newMessage.trim()) return;
    
    const message = {
      id: Date.now(),
      text: newMessage,
      sender: 'current-user',
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, message]);
    setNewMessage('');
    
    // Broadcast to other participants
    };

  const cleanup = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    
    Object.values(peerConnectionsRef.current).forEach(pc => {
      pc.close();
    });
  };

  if (!isCollaborating) {
    return (
      <motion.button
        onClick={() => setIsCollaborating(true)}
        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Users className="w-5 h-5" />
        <span>Bắt đầu cộng tác</span>
      </motion.button>
    );
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm">
      <div className="h-full flex">
        {/* Main Collaboration Area */}
        <div className="flex-1 relative">
          {/* 3D Model Viewer with Annotations */}
          <div className="relative h-full">
            {/* Drawing Canvas Overlay */}
            <canvas
              ref={canvasRef}
              className="absolute inset-0 z-10 pointer-events-auto"
              width={window.innerWidth}
              height={window.innerHeight}
            />
            
            {/* Cursor Positions */}
            {Object.entries(cursorPositions).map(([userId, position]) => (
              <motion.div
                key={userId}
                className="absolute z-20 pointer-events-none"
                style={{ left: position.x, top: position.y }}
                animate={{ x: 0, y: 0 }}
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
              >
                <Cursor className="w-6 h-6 text-blue-500" />
                <div className="bg-blue-500 text-white px-2 py-1 rounded text-xs ml-2">
                  User {userId}
                </div>
              </motion.div>
            ))}
            
            {/* 3D Model Container */}
            <div className="w-full h-full bg-gray-900 flex items-center justify-center">
              <div className="text-white text-center">
                <Monitor className="w-16 h-16 mx-auto mb-4" />
                <h3 className="text-xl font-bold mb-2">3D Model Collaboration</h3>
                <p className="text-gray-400">Model ID: {modelId}</p>
              </div>
            </div>
          </div>
          
          {/* Top Toolbar */}
          <div className="absolute top-4 left-4 right-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Mode Selector */}
              <div className="flex bg-black/50 rounded-lg p-1">
                {Object.entries(modes).map(([key, mode]) => {
                  const IconComponent = mode.icon;
                  return (
                    <button
                      key={key}
                      onClick={() => setCollaborationMode(key)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                        collaborationMode === key
                          ? `bg-${mode.color}-600 text-white`
                          : 'text-gray-300 hover:text-white'
                      }`}
                    >
                      <IconComponent className="w-4 h-4" />
                      <span className="text-sm">{mode.name}</span>
                    </button>
                  );
                })}
              </div>
              
              {/* Drawing Tools */}
              {collaborationMode === 'edit' && (
                <div className="flex bg-black/50 rounded-lg p-1 space-x-1">
                  {drawingTools.map((tool) => (
                    <button
                      key={tool.id}
                      onClick={() => setDrawingTool(tool.id)}
                      className={`p-2 rounded-md transition-colors ${
                        drawingTool === tool.id
                          ? 'bg-green-600 text-white'
                          : 'text-gray-300 hover:text-white'
                      }`}
                      title={tool.name}
                    >
                      <span className="text-lg">{tool.icon}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Participants */}
            <div className="flex items-center space-x-2">
              <div className="bg-black/50 rounded-lg px-3 py-2 text-white">
                <Users className="w-4 h-4 inline mr-2" />
                {participants.length + 1} người tham gia
              </div>
              
              <button
                onClick={() => setIsCollaborating(false)}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Kết thúc
              </button>
            </div>
          </div>
          
          {/* Bottom Controls */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="flex items-center space-x-2 bg-black/50 rounded-lg p-2">
              <button
                onClick={toggleVideo}
                className={`p-3 rounded-lg transition-colors ${
                  isVideoEnabled ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                }`}
              >
                {isVideoEnabled ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
              </button>
              
              <button
                onClick={toggleAudio}
                className={`p-3 rounded-lg transition-colors ${
                  isAudioEnabled ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                }`}
              >
                {isAudioEnabled ? <Mic className="w-5 h-5" /> : <MicOff className="w-5 h-5" />}
              </button>
              
              <button
                onClick={isScreenSharing ? stopScreenShare : startScreenShare}
                className={`p-3 rounded-lg transition-colors ${
                  isScreenSharing ? 'bg-blue-600 text-white' : 'bg-gray-600 text-white hover:bg-gray-500'
                }`}
              >
                <Share2 className="w-5 h-5" />
              </button>
              
              <button
                className="p-3 rounded-lg bg-gray-600 text-white hover:bg-gray-500 transition-colors"
              >
                <Hand className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
        
        {/* Right Sidebar */}
        <div className="w-80 bg-gray-900 border-l border-gray-700 flex flex-col">
          {/* Video Participants */}
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-white font-semibold mb-3">Người tham gia</h3>
            <div className="space-y-2">
              {/* Self Video */}
              <div className="relative bg-gray-800 rounded-lg overflow-hidden aspect-video">
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                  Bạn {isHost && '(Host)'}
                </div>
              </div>
              
              {/* Other Participants */}
              {participants.map((participant) => (
                <div key={participant.id} className="relative bg-gray-800 rounded-lg overflow-hidden aspect-video">
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    <Camera className="w-8 h-8" />
                  </div>
                  <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                    {participant.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Chat */}
          <div className="flex-1 flex flex-col">
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-white font-semibold">Chat</h3>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {chatMessages.map((message) => (
                <div key={message.id} className="text-sm">
                  <div className="text-gray-400 text-xs mb-1">
                    {message.sender} • {message.timestamp.toLocaleTimeString()}
                  </div>
                  <div className="text-white bg-gray-800 rounded-lg p-2">
                    {message.text}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="p-4 border-t border-gray-700">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                  placeholder="Nhập tin nhắn..."
                  className="flex-1 bg-gray-800 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                />
                <button
                  onClick={sendChatMessage}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <MessageCircle className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeCollaboration;
