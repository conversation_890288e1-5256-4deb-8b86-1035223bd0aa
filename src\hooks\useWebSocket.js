import React, { useCallback } from 'react';

/**
 * Dummy WebSocket hook - DISABLED
 * This is a simplified implementation that does nothing to avoid WebSocket connection issues
 *
 * @param {string} url - WebSocket URL (ignored)
 * @param {Object} options - Configuration options (ignored)
 * @returns {Object} Dummy WebSocket state and methods
 */
const ComponentName = memo((props) => {
  // Dummy methods that do nothing
  const sendMessage = useCallback(() => false, []);
  const connect = useCallback(() => {}, []);
  const disconnect = useCallback(() => {}, []);
  const reconnect = useCallback(() => {}, []);

  // Return dummy values
  return {
    isConnected: false,
    lastMessage: null,
    connectionAttempts: 0,
    error: null,
    connectionState: 'CLOSED',
    sendMessage,
    connect,
    disconnect,
    reconnect
  };
};

export default useWebSocket;
