import { useCallback, useRef, useEffect, useMemo, useState } from 'react';
import { PERFORMANCE_CONFIG } from '../config/production';

/**
 * Enhanced performance optimization hook
 */
export const useOptimizedPerformance = () => {
  // Debounce function with configurable delay
  const useDebounce = useCallback((func, delay = PERFORMANCE_CONFIG.DEBOUNCE_DELAY) => {
    const timeoutRef = useRef(null);

    return useCallback((...args) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        func(...args);
      }, delay);
    }, [func, delay]);
  }, []);

  // Throttle function with configurable delay
  const useThrottle = useCallback((func, delay = PERFORMANCE_CONFIG.THROTTLE_DELAY) => {
    const lastCallRef = useRef(0);
    const timeoutRef = useRef(null);

    return useCallback((...args) => {
      const now = Date.now();
      
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        func(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCallRef.current = Date.now();
          func(...args);
        }, delay - (now - lastCallRef.current));
      }
    }, [func, delay]);
  }, []);

  // Intersection Observer for lazy loading
  const useIntersectionObserver = useCallback((callback, options = {}) => {
    const elementRef = useRef(null);
    const observerRef = useRef(null);

    useEffect(() => {
      const element = elementRef.current;
      if (!element) return;

      observerRef.current = new IntersectionObserver(callback, {
        threshold: 0.1,
        rootMargin: `${PERFORMANCE_CONFIG.LAZY_LOAD_THRESHOLD}px`,
        ...options
      });

      observerRef.current.observe(element);

      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
      };
    }, [callback, options]);

    return elementRef;
  }, []);

  // Request Animation Frame optimization
  const useAnimationFrame = useCallback((callback) => {
    const requestRef = useRef(null);
    const previousTimeRef = useRef(null);

    const animate = useCallback((time) => {
      if (previousTimeRef.current !== undefined) {
        const deltaTime = time - previousTimeRef.current;
        callback(deltaTime);
      }
      previousTimeRef.current = time;
      requestRef.current = requestAnimationFrame(animate);
    }, [callback]);

    useEffect(() => {
      requestRef.current = requestAnimationFrame(animate);
      return () => {
        if (requestRef.current) {
          cancelAnimationFrame(requestRef.current);
        }
      };
    }, [animate]);

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);

  return {
    useDebounce,
    useThrottle,
    useIntersectionObserver,
    useAnimationFrame
  };
};

/**
 * Hook for API request optimization
 */
export const useAPIOptimization = () => {
  const requestCache = useRef(new Map());
  const abortControllers = useRef(new Map());

  // Deduplicate API requests
  const deduplicateRequest = useCallback((key, requestFn) => {
    if (requestCache.current.has(key)) {
      return requestCache.current.get(key);
    }

    const promise = requestFn().finally(() => {
      requestCache.current.delete(key);
    });

    requestCache.current.set(key, promise);
    return promise;
  }, []);

  // Cancel previous requests
  const cancelPreviousRequests = useCallback((key) => {
    if (abortControllers.current.has(key)) {
      abortControllers.current.get(key).abort();
    }

    const controller = new AbortController();
    abortControllers.current.set(key, controller);
    return controller.signal;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      abortControllers.current.forEach(controller => controller.abort());
      requestCache.current.clear();
      abortControllers.current.clear();
    };
  }, []);

  return {
    deduplicateRequest,
    cancelPreviousRequests
  };
};

/**
 * Memory monitoring hook
 */
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = useState(null);

  useEffect(() => {
    const checkMemory = () => {
      if (performance.memory) {
        setMemoryInfo({
          used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
        });
      }
    };

    checkMemory();
    const interval = setInterval(checkMemory, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

/**
 * Component performance monitoring
 */
export const useComponentPerformance = (componentName) => {
  const renderCount = useRef(0);
  const startTime = useRef(performance.now());

  useEffect(() => {
    renderCount.current += 1;
    const renderTime = performance.now() - startTime.current;

    // Only warn in development and if render time is significant
    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(`${componentName} render took ${renderTime.toFixed(2)}ms (render #${renderCount.current})`);
    }

    startTime.current = performance.now();
  });

  return {
    renderCount: renderCount.current,
    logPerformance: () => ({
      component: componentName,
      renders: renderCount.current,
      averageRenderTime: (performance.now() - startTime.current) / renderCount.current
    })
  };
};

/**
 * Optimized scroll handler
 */
export const useOptimizedScroll = (callback, dependencies = []) => {
  const { useThrottle } = useOptimizedPerformance();
  const throttledCallback = useThrottle(callback, PERFORMANCE_CONFIG.THROTTLE_DELAY);

  useEffect(() => {
    window.addEventListener('scroll', throttledCallback, { passive: true });
    return () => window.removeEventListener('scroll', throttledCallback);
  }, dependencies);
};

/**
 * Optimized resize handler
 */
export const useOptimizedResize = (callback, dependencies = []) => {
  const { useDebounce } = useOptimizedPerformance();
  const debouncedCallback = useDebounce(callback, PERFORMANCE_CONFIG.DEBOUNCE_DELAY);

  useEffect(() => {
    window.addEventListener('resize', debouncedCallback, { passive: true });
    return () => window.removeEventListener('resize', debouncedCallback);
  }, dependencies);
};

export default useOptimizedPerformance;
