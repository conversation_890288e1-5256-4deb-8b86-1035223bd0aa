import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiImage, FiMaximize2, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import ImageGallery from '../common/ImageGallery';

const ComponentName = memo((props) => {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Prepare images array
  const images = React.useMemo(() => {
    const imageList = [];

    // Add main image if exists
    if (model.imageUrl) {
      imageList.push({
        url: model.imageUrl,
        filename: `${model.title} - Main Image`,
        isMain: true
      });
    }

    // Add preview images
    if (model.previewImages && model.previewImages.length > 0) {
      model.previewImages.forEach((img, index) => {
        if (!img.isMain || !model.imageUrl) { // Avoid duplicating main image
          imageList.push({
            url: img.url,
            filename: img.filename || `${model.title} - Image ${index + 1}`,
            thumbnails: img.thumbnails,
            isMain: img.isMain
          });
        }
      });
    }

    return imageList;
  }, [model]);

  const openGallery = (index = 0) => {
    setSelectedImageIndex(index);
    setIsGalleryOpen(true);
  };

  if (images.length === 0) {
    return (
      <div className={`bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center p-8">
          <FiImage className="h-12 w-12 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500 dark:text-gray-400">No preview images</p>
        </div>
      </div>
    );
  }

  const mainImage = images[0];

  return (
    <>
      <div className={`relative ${className}`}>
        {/* Main Image */}
        <div className="relative group cursor-pointer" onClick={() => openGallery(0)}>
          <img
            src={mainImage.url}
            alt={model.title}
            className="w-full h-full object-cover rounded-lg"
          />

          {/* Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 rounded-lg flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="flex items-center gap-2 text-white">
                <FiMaximize2 className="h-6 w-6" />
                <span className="text-sm font-medium">View Gallery</span>
              </div>
            </div>
          </div>

          {/* Image Count Badge */}
          {images.length > 1 && (
            <div className="absolute top-3 right-3 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
              {images.length} images
            </div>
          )}
        </div>

        {/* Thumbnail Strip (for multiple images) */}
        {images.length > 1 && (
          <div className="mt-3">
            <div className="flex gap-2 overflow-x-auto pb-2">
              {images.slice(0, 5).map((image, index) => (
                <button
                  key={index}
                  onClick={() => openGallery(index)}
                  className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-500 transition-colors"
                >
                  <img
                    src={image.thumbnails?.small?.url || image.url}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}

              {images.length > 5 && (
                <button
                  onClick={() => openGallery(5)}
                  className="flex-shrink-0 w-16 h-16 rounded-lg bg-gray-100 dark:bg-gray-700 border-2 border-transparent hover:border-blue-500 transition-colors flex items-center justify-center"
                >
                  <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                    +{images.length - 5}
                  </span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Image Gallery Modal */}
      <ImageGallery
        images={images}
        isOpen={isGalleryOpen}
        onClose={() => setIsGalleryOpen(false)}
        initialIndex={selectedImageIndex}
      />
    </>
  );
};

export default ModelImagePreview;
