import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiSearch, FiClock, FiTrendingUp, FiX } from 'react-icons/fi';

const ComponentName = memo((props) => {
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [trendingSearches] = useState([
    'Kitchen 3D Models',
    'Living Room Furniture',
    'Modern Architecture',
    'Bathroom Design',
    'Office Interior',
    'Garden Landscape',
    'Bedroom Furniture',
    'Restaurant Design'
  ]);

  const containerRef = useRef(null);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Generate suggestions based on query
  useEffect(() => {
    if (query.trim()) {
      const filtered = useMemo(() => trendingSearches.filter(item =>
        item.toLowerCase().includes(query.toLowerCase())
      ), [dependencies]);
      setSuggestions(filtered.slice(0, 5));
    } else {
      setSuggestions([]);
    }
  }, [query, trendingSearches]);

  // Save search to recent searches
  const saveRecentSearch = (searchTerm) => {
  const updated = [searchTerm, ...recentSearches.filter(item => item !== searchTerm)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion) => {
  saveRecentSearch(suggestion);
    onSuggestionClick(suggestion);
  };

  // Clear recent searches
  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        ref={containerRef}
        initial={{ opacity: 0, y: -10, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -10, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="absolute top-full left-0 right-0 mt-2 glass-card rounded-2xl border border-white/20 shadow-professional-lg z-50 overflow-hidden"
      >
        {/* Search Suggestions */}
        {query.length > 0 && suggestions.length > 0 && (
          <div className="p-4">
            <div className="flex items-center mb-3">
              <FiSearch className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Gợi ý tìm kiếm
              </span>
            </div>
            <div className="space-y-1">
              {suggestions.map((suggestion, index) => (
                <motion.button
                  key={suggestion}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-3 py-2 rounded-xl hover:bg-white/20 transition-all duration-200 group flex items-center"
                >
                  <FiSearch className="h-4 w-4 text-gray-400 mr-3 group-hover:text-blue-600 transition-colors" />
                  <span className="text-gray-700 dark:text-gray-300 group-hover:text-blue-600 transition-colors">
                    {suggestion}
                  </span>
                </motion.button>
              ))}
            </div>
          </div>
        )}

        {/* Recent Searches */}
        {query.length === 0 && recentSearches.length > 0 && (
          <div className="p-4 border-t border-white/10">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <FiClock className="h-4 w-4 text-gray-600 mr-2" />
                <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Tìm kiếm gần đây
                </span>
              </div>
              <button
                onClick={clearRecentSearches}
                className="text-xs text-gray-500 hover:text-red-600 transition-colors flex items-center"
              >
                <FiX className="h-3 w-3 mr-1" />
                Xóa
              </button>
            </div>
            <div className="space-y-1">
              {recentSearches.map((search, index) => (
                <motion.button
                  key={search}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={() => handleSuggestionClick(search)}
                  className="w-full text-left px-3 py-2 rounded-xl hover:bg-white/20 transition-all duration-200 group flex items-center"
                >
                  <FiClock className="h-4 w-4 text-gray-400 mr-3 group-hover:text-blue-600 transition-colors" />
                  <span className="text-gray-700 dark:text-gray-300 group-hover:text-blue-600 transition-colors">
                    {search}
                  </span>
                </motion.button>
              ))}
            </div>
          </div>
        )}

        {/* Trending Searches */}
        {query.length === 0 && (
          <div className="p-4 border-t border-white/10">
            <div className="flex items-center mb-3">
              <FiTrendingUp className="h-4 w-4 text-orange-600 mr-2" />
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Xu hướng tìm kiếm
              </span>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {trendingSearches.slice(0, 6).map((trend, index) => (
                <motion.button
                  key={trend}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={() => handleSuggestionClick(trend)}
                  className="text-left px-3 py-2 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-800/30 dark:hover:to-purple-800/30 transition-all duration-200 group"
                >
                  <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-blue-600 transition-colors">
                    {trend}
                  </span>
                </motion.button>
              ))}
            </div>
          </div>
        )}

        {/* No results */}
        {query.length > 0 && suggestions.length === 0 && (
          <div className="p-4 text-center">
            <div className="text-gray-500 dark:text-gray-400">
              <FiSearch className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Không tìm thấy gợi ý nào cho "{query}"</p>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="px-4 py-3 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10 border-t border-white/10">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Nhấn Enter để tìm kiếm hoặc ESC để đóng
          </p>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SearchSuggestions;
