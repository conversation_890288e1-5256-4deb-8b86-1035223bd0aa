// Webpack optimization configuration
export const webpackOptimizations = {
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all',
        maxSize: 244000, // 244KB
      },
      common: {
        name: 'common',
        minChunks: 2,
        chunks: 'all',
        enforce: true,
        maxSize: 244000,
      }
    }
  },
  usedExports: true,
  sideEffects: false,
  minimize: true,
  concatenateModules: true
};

export default webpackOptimizations;