import React, { lazy, useEffect, Suspense, useState } from 'react';

// Import other dependencies
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './context/AuthContext';
import { ModelProvider } from './context/ModelContext';
import { PaymentProvider } from './context/PaymentContext';

// Import components
import Header from './components/Header';
import Footer from './components/Footer';
import Chatbot from './components/Chatbot';
import ErrorBoundary from './components/ErrorBoundary';
import ClientErrorBoundary from './components/ClientErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';
import PerformancePanel from './components/dev/PerformancePanel';

// Import performance optimization and error handling
import { initializePerformanceOptimizations } from './utils/performanceOptimizer';
import { setupGlobalErrorHandler } from './utils/errorHandling';

// Import API services for health checks
import apiService from './services/api';
import realDataService from './services/realDataService';

// Import styles
import './App.css';

// Enhanced Loading component with skeleton UI and progress indicator
const LoadingFallback = ({ message = "Loading..." }) => {
  const [progress, setProgress] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState(message);

  useEffect(() => {
    const messages = [
      "Connecting to 3DSKETCHUP.NET...",
      "Loading models from database...",
      "Preparing your experience...",
      "Almost ready!"
    ];

    let messageIndex = 0;
    let progressValue = 0;

    const interval = setInterval(() => {
      progressValue += Math.random() * 30;
      if (progressValue > 90) progressValue = 90;
      setProgress(progressValue);

      if (progressValue > 25 * (messageIndex + 1) && messageIndex < messages.length - 1) {
        messageIndex++;
        setLoadingMessage(messages[messageIndex]);
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="w-full max-w-md mx-auto px-6">
        <div className="text-center">
          {/* Logo */}
          <div className="mb-8">
            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
            <h2 className="mt-4 text-2xl font-bold text-gray-900 dark:text-white">
              3DSKETCHUP.NET
            </h2>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {Math.round(progress)}% Complete
            </p>
          </div>

          {/* Loading Message */}
          <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">
            {loadingMessage}
          </p>

          {/* Skeleton Content */}
          <div className="space-y-3">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mx-auto"></div>
            </div>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mx-auto"></div>
            </div>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mx-auto"></div>
            </div>
          </div>

          {/* Spinning Icon */}
          <div className="mt-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Lazy load pages with improved performance
const Home = lazy(() => import(/* webpackChunkName: "Home" */ './pages/Home'));
const NewHome = lazy(() => import(/* webpackChunkName: "NewHome" */ './pages/NewHome'));
const Login = lazy(() => import(/* webpackChunkName: "Login" */ './pages/Login'));
const Register = lazy(() => import(/* webpackChunkName: "Register" */ './pages/Register'));
const ForgotPassword = lazy(() => import(/* webpackChunkName: "ForgotPassword" */ './pages/ForgotPassword'));
const ResetPassword = lazy(() => import(/* webpackChunkName: "ResetPassword" */ './pages/ResetPassword'));
const ModelDetail = lazy(() => import(/* webpackChunkName: "ModelDetail" */ './pages/ModelDetail'));
const Profile = lazy(() => import(/* webpackChunkName: "Profile" */ './pages/Profile'));
const SavedModels = lazy(() => import(/* webpackChunkName: "SavedModels" */ './pages/SavedModels'));
const Downloads = lazy(() => import(/* webpackChunkName: "Downloads" */ './pages/Downloads'));
const Subscription = lazy(() => import(/* webpackChunkName: "Subscription" */ './pages/Subscription'));
const UploadModel = lazy(() => import(/* webpackChunkName: "UploadModel" */ './pages/UploadModel'));
const CategoryPage = lazy(() => import(/* webpackChunkName: "CategoryPage" */ './pages/CategoryPage'));
const SearchResults = lazy(() => import(/* webpackChunkName: "SearchResults" */ './pages/SearchResults'));
const SearchPage = lazy(() => import(/* webpackChunkName: "SearchPage" */ './pages/SearchPage'));
const VirtualShowrooms = lazy(() => import(/* webpackChunkName: "VirtualShowrooms" */ './pages/VirtualShowrooms'));
const SmartCollections = lazy(() => import(/* webpackChunkName: "SmartCollections" */ './pages/SmartCollections'));
const Warehouse = lazy(() => import(/* webpackChunkName: "Warehouse" */ './pages/Warehouse'));
const Plugins = lazy(() => import(/* webpackChunkName: "Plugins" */ './pages/Plugins'));
const FeaturesDemo = lazy(() => import(/* webpackChunkName: "FeaturesDemo" */ './pages/FeaturesDemo'));
const NotFound = lazy(() => import(/* webpackChunkName: "NotFound" */ './pages/NotFound'));

// Admin pages
const AdminDashboard = lazy(() => import(/* webpackChunkName: "AdminDashboard" */ './pages/admin/AdminDashboard'));
const DashboardOverview = lazy(() => import(/* webpackChunkName: "DashboardOverview" */ './pages/admin/DashboardOverview'));
const UserManagement = lazy(() => import(/* webpackChunkName: "UserManagement" */ './pages/admin/UserManagement'));
const AddUser = lazy(() => import(/* webpackChunkName: "AddUser" */ './pages/admin/AddUser'));
const UserRoles = lazy(() => import(/* webpackChunkName: "UserRoles" */ './pages/admin/UserRoles'));
const ModelManagement = lazy(() => import(/* webpackChunkName: "ModelManagement" */ './pages/admin/ModelManagement'));
const AddModel = lazy(() => import(/* webpackChunkName: "AddModel" */ './pages/admin/AddModel'));
const ModelCategories = lazy(() => import(/* webpackChunkName: "ModelCategories" */ './pages/admin/ModelCategories'));
const Analytics = lazy(() => import(/* webpackChunkName: "Analytics" */ './pages/admin/Analytics'));
const DownloadAnalytics = lazy(() => import(/* webpackChunkName: "DownloadAnalytics" */ './pages/admin/DownloadAnalytics'));
const RevenueAnalytics = lazy(() => import(/* webpackChunkName: "RevenueAnalytics" */ './pages/admin/RevenueAnalytics'));
const Settings = lazy(() => import(/* webpackChunkName: "Settings" */ './pages/admin/Settings'));

// Information pages
const About = lazy(() => import(/* webpackChunkName: "About" */ './pages/About'));
const Contact = lazy(() => import(/* webpackChunkName: "Contact" */ './pages/Contact'));
const Terms = lazy(() => import(/* webpackChunkName: "Terms" */ './pages/Terms'));
const Privacy = lazy(() => import(/* webpackChunkName: "Privacy" */ './pages/Privacy'));

// Test pages
const ImageSearchTest = lazy(() => import(/* webpackChunkName: "ImageSearchTest" */ './components/test/ImageSearchTest'));
const AdminAddModel = lazy(() => import(/* webpackChunkName: "AdminAddModel" */ './pages/AdminAddModel'));
const AdminAddModelSimple = lazy(() => import(/* webpackChunkName: "AdminAddModelSimple" */ './pages/AdminAddModelSimple'));

// AppRoutes component
function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<NewHome />} />
      <Route path="/old" element={<Home />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password/:token" element={<ResetPassword />} />
      <Route path="/model/:id" element={<ModelDetail />} />
      <Route path="/profile" element={
        <ProtectedRoute>
          <Profile />
        </ProtectedRoute>
      } />
      <Route path="/saved-models" element={
        <ProtectedRoute>
          <SavedModels />
        </ProtectedRoute>
      } />
      <Route path="/downloads" element={
        <ProtectedRoute>
          <Downloads />
        </ProtectedRoute>
      } />
      <Route path="/subscription" element={
        <ProtectedRoute>
          <Subscription />
        </ProtectedRoute>
      } />
      <Route path="/upload" element={
        <ProtectedRoute
          adminOnly={true}
          requiredPermission="upload"
          unauthorizedMessage="Only administrators can upload models"
        >
          <UploadModel />
        </ProtectedRoute>
      } />

      {/* Category Routes */}
      <Route path="/models" element={<CategoryPage />} />
      <Route path="/category/:category" element={<CategoryPage />} />
      <Route path="/search" element={<SearchPage />} />
      <Route path="/search-results" element={<SearchResults />} />
      <Route path="/showrooms" element={<VirtualShowrooms />} />
      <Route path="/collections" element={<SmartCollections />} />
      <Route path="/warehouse" element={<Warehouse />} />
      <Route path="/plugins" element={<Plugins />} />
      <Route path="/features-demo" element={<FeaturesDemo />} />

      {/* Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute adminOnly={true}>
          <AdminDashboard />
        </ProtectedRoute>
      }>
        <Route index element={<DashboardOverview />} />
        <Route path="users" element={<UserManagement />} />
        <Route path="users/add" element={<AddUser />} />
        <Route path="users/roles" element={<UserRoles />} />
        <Route path="models" element={<ModelManagement />} />
        <Route path="models/add" element={<AddModel />} />
        <Route path="models/categories" element={<ModelCategories />} />
        <Route path="analytics" element={<Analytics />} />
        <Route path="analytics/downloads" element={<DownloadAnalytics />} />
        <Route path="analytics/revenue" element={<RevenueAnalytics />} />
        <Route path="settings" element={<Settings />} />
      </Route>

      {/* Information pages */}
      <Route path="/about" element={<About />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />

      {/* Test pages */}
      <Route path="/test/image-search" element={<ImageSearchTest />} />
      <Route path="/admin/add-model" element={
        <ProtectedRoute adminOnly={true}>
          <AdminAddModel />
        </ProtectedRoute>
      } />
      <Route path="/admin/add-model-simple" element={
        <ProtectedRoute adminOnly={true}>
          <AdminAddModelSimple />
        </ProtectedRoute>
      } />

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

// System Health Monitor Component
const SystemHealthMonitor = () => {
  const [systemHealth, setSystemHealth] = useState({
    backend: 'checking',
    database: 'checking',
    api: 'checking'
  });

  useEffect(() => {
    const checkSystemHealth = async () => {
      try {
        // Check backend health
        const backendResponse = await fetch('http://localhost:5002/api/health-check');
        const backendHealthy = backendResponse.ok;

        // Check API endpoints
        const apiResponse = await realDataService.getStats();
        const apiHealthy = !!apiResponse;

        setSystemHealth({
          backend: backendHealthy ? 'healthy' : 'error',
          database: apiHealthy ? 'healthy' : 'error',
          api: apiHealthy ? 'healthy' : 'error'
        });
      } catch (error) {
        setSystemHealth({
          backend: 'error',
          database: 'error',
          api: 'error'
        });
      }
    };

    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (Object.values(systemHealth).every(status => status === 'healthy')) {
    return null; // Don't show if everything is healthy
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-yellow-500 text-white px-4 py-2 text-center text-sm">
      System Status: Backend {systemHealth.backend}, Database {systemHealth.database}, API {systemHealth.api}
    </div>
  );
};

// Main App component with enhanced features
function App() {
  const [isAppReady, setIsAppReady] = useState(false);
  const [initializationProgress, setInitializationProgress] = useState(0);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Step 1: Setup global error handler
        setupGlobalErrorHandler();
        setInitializationProgress(20);

        // Step 2: Initialize performance optimizations
        initializePerformanceOptimizations();
        setInitializationProgress(40);

        // Step 3: Setup dark mode
        const darkMode = localStorage.getItem('darkMode');
        if (darkMode === 'true' || (!darkMode && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
        setInitializationProgress(60);

        // Step 4: Add page transition class
        document.body.classList.add('page-transition-enhanced');
        setInitializationProgress(70);

        // Step 5: Check API connectivity
        try {
          await realDataService.getStats();
          setInitializationProgress(85);
        } catch (error) {
          setInitializationProgress(85);
        }

        // Step 6: Unregister service workers for clean state
        if ('serviceWorker' in navigator) {
          try {
            const registrations = await navigator.serviceWorker.getRegistrations();
            for (let registration of registrations) {
              await registration.unregister();
            }

            // Clear any service worker caches
            if ('caches' in window) {
              const cacheNames = await caches.keys();
              await Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)));
            }
          } catch (error) {
            }
        }
        setInitializationProgress(95);

        // Step 7: Preload critical data
        try {
          await Promise.all([
            realDataService.getAllModels(),
            realDataService.getCategories(),
            realDataService.getFeaturedModels()
          ]);
        } catch (error) {
          }
        setInitializationProgress(100);

        // Small delay to show completion
        setTimeout(() => {
          setIsAppReady(true);
        }, 500);

      } catch (error) {
        // Continue anyway
        setIsAppReady(true);
      }
    };

    initializeApp();
  }, []);

  // Show loading screen while initializing
  if (!isAppReady) {
    return <LoadingFallback message="Initializing 3DSKETCHUP.NET..." />;
  }

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <AuthProvider>
          <ModelProvider>
            <PaymentProvider>
              <Router>
                <div className="flex flex-col min-h-screen transition-colors duration-200 bg-white dark:bg-gray-900">
                  {/* System Health Monitor */}
                  <SystemHealthMonitor />

                  {/* Header */}
                  <Header />

                  {/* Main Content with proper spacing */}
                  <main className="flex-1 main-content relative">
                    <Suspense fallback={<LoadingFallback message="Loading page..." />}>
                      <ErrorBoundary>
                        <AppRoutes />
                      </ErrorBoundary>
                    </Suspense>
                  </main>

                  {/* Footer */}
                  <Footer />

                  {/* Chatbot component wrapped in ClientErrorBoundary */}
                  <ClientErrorBoundary>
                    <Chatbot />
                  </ClientErrorBoundary>

                  {/* Performance Panel for Development */}
                  <PerformancePanel />

                  {/* Toast Notifications */}
                  <Toaster
                    position="top-right"
                    toastOptions={{
                      duration: 4000,
                      style: {
                        background: 'var(--toast-bg)',
                        color: 'var(--toast-color)',
                        border: '1px solid var(--toast-border)',
                      },
                      success: {
                        iconTheme: {
                          primary: '#10B981',
                          secondary: '#FFFFFF',
                        },
                      },
                      error: {
                        iconTheme: {
                          primary: '#EF4444',
                          secondary: '#FFFFFF',
                        },
                      },
                    }}
                  />

                  {/* Global CSS Variables for Toast */}
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      :root {
                        --toast-bg: #ffffff;
                        --toast-color: #374151;
                        --toast-border: #e5e7eb;
                      }

                      .dark {
                        --toast-bg: #1f2937;
                        --toast-color: #f9fafb;
                        --toast-border: #374151;
                      }

                      /* Enhanced page transitions */
                      .page-transition-enhanced {
                        transition: all 0.3s ease-in-out;
                      }

                      /* Smooth scrolling */
                      html {
                        scroll-behavior: smooth;
                      }

                      /* Custom scrollbar */
                      ::-webkit-scrollbar {
                        width: 8px;
                      }

                      ::-webkit-scrollbar-track {
                        background: #f1f5f9;
                      }

                      .dark ::-webkit-scrollbar-track {
                        background: #1e293b;
                      }

                      ::-webkit-scrollbar-thumb {
                        background: #cbd5e1;
                        border-radius: 4px;
                      }

                      .dark ::-webkit-scrollbar-thumb {
                        background: #475569;
                      }

                      ::-webkit-scrollbar-thumb:hover {
                        background: #94a3b8;
                      }

                      .dark ::-webkit-scrollbar-thumb:hover {
                        background: #64748b;
                      }
                    `
                  }} />
                </div>
              </Router>
            </PaymentProvider>
          </ModelProvider>
        </AuthProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;