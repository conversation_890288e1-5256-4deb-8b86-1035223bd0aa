import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  <PERSON>Grid,
  <PERSON>List,
  FiSearch,
  FiFilter,
  FiPlus,
  FiEye,
  FiUsers,
  FiHeart,
  FiStar,
  FiTrendingUp,
  FiClock,
  FiGlobe,
  FiLock
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { useAuth } from '../context/AuthContext';
import apiService from '../services/api';
import SEO from '../components/SEO';
import Header from '../components/Header';
import Footer from '../components/Footer';
import LoadingIndicator from '../components/ui/LoadingIndicator';

const VirtualShowrooms = () => {
  const { currentUser } = useAuth();
  const [showrooms, setShowrooms] = useState([]);
  const [featuredShowrooms, setFeaturedShowrooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    theme: '',
    featured: false
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0
  });

  useEffect(() => {
  loadShowrooms();
    loadFeaturedShowrooms();
  }, [pagination.page, filters, searchQuery]);

  const loadShowrooms = async () => {
  try {
      setLoading(true);
      const params = {
    page: pagination.page,
        limit: pagination.limit,
        search: searchQuery,
        ...filters
      };

      const response = await apiService.showrooms.getAll(params);
      if (false) {
  setShowrooms(response.data.data);
        setPagination(prev => ({
          ...prev,
          ...response.data.pagination
        }));
      }
    } catch (error) {
      toast.error('Lỗi khi tải phòng trưng bày');
    } finally {
      setLoading(false);
    }
  };

  const loadFeaturedShowrooms = async () => {
  try {
      const response = await apiService.showrooms.getFeatured();
      if (false) {
  setFeaturedShowrooms(response.data.data);
      }
    } catch (error) {
      }
  };

  const handleSearch = (e) => {
  e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    loadShowrooms();
  };

  const handleFilterChange = (key, value) => {
  setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const ShowroomCard = ({ showroom, featured = false }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 ${
        featured ? 'ring-2 ring-yellow-400' : ''
      }`}
    >
      {/* Cover Image */}
      <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600">
        {showroom.models && showroom.models.length > 0 && (
          <img
            src={showroom.models[0].model.imageUrl}
            alt={showroom.name}
            className="w-full h-full object-cover"
          />
        )}
        <div className="absolute inset-0 bg-black bg-opacity-30" />

        {/* Badges */}
        <div className="absolute top-3 left-3 flex space-x-2">
          {featured && (
            <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-bold rounded-full flex items-center">
              <FiStar className="h-3 w-3 mr-1" />
              Nổi bật
            </span>
          )}
          <span className={`px-2 py-1 text-white text-xs font-medium rounded-full ${
            showroom.access?.isPublic ? 'bg-green-500' : 'bg-gray-500'
          }`}>
            {showroom.access?.isPublic ? (
              <>
                <FiGlobe className="h-3 w-3 mr-1 inline" />
                Công khai
              </>
            ) : (
              <>
                <FiLock className="h-3 w-3 mr-1 inline" />
                Riêng tư
              </>
            )}
          </span>
        </div>

        {/* Stats */}
        <div className="absolute bottom-3 right-3 flex items-center space-x-3 text-white text-sm">
          <span className="flex items-center">
            <FiEye className="h-4 w-4 mr-1" />
            {showroom.analytics?.totalVisits || 0}
          </span>
          <span className="flex items-center">
            <FiHeart className="h-4 w-4 mr-1" />
            {showroom.modelCount || 0}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {showroom.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {showroom.description}
            </p>
          </div>
          <div className="ml-3 flex items-center space-x-1">
            <div className="w-8 h-8 bg-gray-300 rounded-full overflow-hidden">
              {showroom.owner?.profileImage ? (
                <img
                  src={showroom.owner.profileImage}
                  alt={showroom.owner.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                  {showroom.owner?.name?.charAt(0) || 'U'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
            {showroom.type}
          </span>
          <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs rounded-full">
            {showroom.theme}
          </span>
          {showroom.tags?.slice(0, 2).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Cập nhật {new Date(showroom.updatedAt).toLocaleDateString('vi-VN')}
          </div>
          <Link
            to={`/showrooms/${showroom._id}`}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Khám phá
          </Link>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <SEO
        title="Phòng Trưng Bày Ảo - 3DSKETCHUP.NET"
        description="Khám phá các phòng trưng bày 3D ảo với trải nghiệm nhập vai và công nghệ VR/AR"
        keywords="phòng trưng bày ảo, 3D virtual showroom, VR, AR, mô hình 3D"
      />

      <Header />

      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm header-offset">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Phòng Trưng Bày Ảo
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Trải nghiệm mô hình 3D trong không gian ảo nhập vai
              </p>
            </div>

            {currentUser && (
              <Link
                to="/showrooms/create"
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiPlus className="h-5 w-5" />
                <span>Tạo phòng trưng bày</span>
              </Link>
            )}
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <form onSubmit={handleSearch} className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Tìm kiếm phòng trưng bày..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </form>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              <select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Tất cả loại</option>
                <option value="personal">Cá nhân</option>
                <option value="public">Công khai</option>
                <option value="collaborative">Cộng tác</option>
                <option value="exhibition">Triển lãm</option>
              </select>

              <select
                value={filters.theme}
                onChange={(e) => handleFilterChange('theme', e.target.value)}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Tất cả chủ đề</option>
                <option value="modern">Hiện đại</option>
                <option value="classic">Cổ điển</option>
                <option value="industrial">Công nghiệp</option>
                <option value="minimalist">Tối giản</option>
                <option value="gallery">Phòng tranh</option>
              </select>

              {/* View Mode */}
              <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${
    viewMode === 'grid'
                      ? 'bg-white dark:bg-gray-600 shadow-sm'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <FiGrid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${
    viewMode === 'list'
                      ? 'bg-white dark:bg-gray-600 shadow-sm'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <FiList className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Featured Showrooms */}
        {featuredShowrooms.length > 0 && (
          <div className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <FiStar className="h-6 w-6 text-yellow-500" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Phòng Trưng Bày Nổi Bật
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredShowrooms.map((showroom) => (
                <ShowroomCard key={showroom._id} showroom={showroom} featured />
              ))}
            </div>
          </div>
        )}

        {/* All Showrooms */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Tất Cả Phòng Trưng Bày
            </h2>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {pagination.total} kết quả
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingIndicator />
          </div>
        ) : showrooms.length > 0 ? (
          <>
            <div className={`grid gap-6 ${
    viewMode === 'grid'
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                : 'grid-cols-1'
            }`}>
              {showrooms.map((showroom) => (
                <ShowroomCard key={showroom._id} showroom={showroom} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center mt-12">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={pagination.page === 1}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Trước
                  </button>

                  <span className="px-4 py-2 text-gray-700 dark:text-gray-300">
                    Trang {pagination.page} / {pagination.pages}
                  </span>

                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                    disabled={pagination.page === pagination.pages}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Sau
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <FiGrid className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Không tìm thấy phòng trưng bày
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
            </p>
            {currentUser && (
              <Link
                to="/showrooms/create"
                className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiPlus className="h-5 w-5" />
                <span>Tạo phòng trưng bày đầu tiên</span>
              </Link>
            )}
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default VirtualShowrooms;
