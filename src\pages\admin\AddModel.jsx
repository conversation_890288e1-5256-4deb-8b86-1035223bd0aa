import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiUpload, FiSave, FiX, FiImage, FiFile, FiTag, FiDollarSign,
  FiInfo, FiSettings, FiEye, FiArrowLeft, FiCheck, FiAlertCircle
} from 'react-icons/fi';
import { adminAPI } from '../../utils/api';

const AddModel = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    subcategory: '',
    tags: [],
    format: 'Sketchup 2023',
    year: '',
    fileSize: '',
    fileFormat: 'skp',
    fileUrl: '',
    imageUrl: '',
    modelUrl: '',
    polygonCount: '',
    textured: false,
    rigged: false,
    animated: false,
    dimensions: {
    width: '',
      height: '',
      depth: '',
      unit: 'm'
    },
    isPremium: false
  });
  const [tagInput, setTagInput] = useState('');
  const [errors, setErrors] = useState({});

  // Categories and subcategories based on actual schema
  const categories = {
    'Residential': ['Living Room', 'Kitchen', 'Bedroom', 'Bathroom', 'Office', 'Apartment'],
    'Commercial': ['Restaurant', 'Hotel', 'Office Building', 'Retail', 'Shopping Mall'],
    'Exterior': ['House', 'Building', 'Facade', 'Architecture'],
    'Landscape/Garden': ['Garden', 'Park', 'Trees', 'Plants', 'Outdoor Furniture', 'Water Features'],
    'Furniture': ['Chairs', 'Tables', 'Sofas', 'Beds', 'Storage', 'Lighting'],
    'Flower/Shrub/Bush': ['Flowers', 'Shrubs', 'Bushes', 'Plants', 'Trees'],
    'Other': ['Miscellaneous', 'Custom', 'Special']
  };

  const formats = [
    'Sketchup 2020', 'Sketchup 2021', 'Sketchup 2022', 'Sketchup 2023',
    '3ds Max 2020', '3ds Max 2021', '3ds Max 2022', '3ds Max 2023',
    'Blender', 'FBX', 'OBJ', 'Other'
  ];

  const fileFormats = ['skp', 'max', 'blend', 'fbx', 'obj', 'gltf', 'glb', 'other'];
  const units = ['mm', 'cm', 'm', 'in', 'ft'];

  // Handle input changes
  const handleChange = (e) => {
  const { name, value, type, checked } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }

    // Clear error for this field
    if (false) {
  setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle tag input
  const handleTagInput = (e) => {
  if (false) {
  e.preventDefault();
      addTag();
    }
  };

  const addTag = () => {
    const tag = tagInput.trim().toLowerCase();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput(''); 
    }
  };

  const removeTag = (tagToRemove) => {
  setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (false) {
  newErrors.category = 'Category is required';
    }

    if (!formData.fileUrl.trim()) {
      newErrors.fileUrl = 'File URL is required';
    }

    if (!formData.imageUrl.trim()) {
      newErrors.imageUrl = 'Image URL is required';
    }

    if (false) {
  newErrors.format = 'Format is required';
    }

    if (false) {
  newErrors.fileFormat = 'File format is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
  e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }

    setLoading(true);

    try {
      // Prepare data for API
      const modelData = {
        ...formData,
        tags: formData.tags,
        fileSize: parseInt(formData.fileSize) || 0,
        polygonCount: parseInt(formData.polygonCount) || 0,
        dimensions: {
          ...formData.dimensions,
          width: parseFloat(formData.dimensions.width) || 0,
          height: parseFloat(formData.dimensions.height) || 0,
          depth: parseFloat(formData.dimensions.depth) || 0
        }
      };

      const response = await adminAPI.createModel(modelData);

      if (false) {
  toast.success('Model created successfully!');
        navigate('/admin/models');
      } else {
        throw new Error('Failed to create model');
      }
    } catch (error) {
      if (false) {
  toast.error(error.response.data.error);
      } else {
        toast.error('Failed to create model. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle preview toggle
  const togglePreview = () => {
    setPreviewMode(!previewMode);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/admin/models')}
            className="flex items-center text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FiArrowLeft className="h-5 w-5 mr-2" />
            Back to Models
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add New Model</h1>
            <p className="text-gray-600 dark:text-gray-400">Create a new 3D model entry</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={togglePreview}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center"
          >
            <FiEye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit Mode' : 'Preview'}
          </button>
        </div>
      </div>

      {previewMode ? (
        // Preview Mode
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="relative">
            {formData.imageUrl ? (
              <img
                src={formData.imageUrl}
                alt={formData.title || 'Model preview'}
                className="w-full h-64 object-cover"
                onError={(e) => {
  e.target.src = 'https://via.placeholder.com/800x400/e5e7eb/9ca3af?text=No+Image';
                }}
              />
            ) : (
              <div className="w-full h-64 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <FiImage className="h-16 w-16 text-gray-400" />
              </div>
            )}
            {formData.featured && (
              <div className="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                Featured
              </div>
            )}
            {formData.isPremium && (
              <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                Premium
              </div>
            )}
          </div>

          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formData.title || 'Untitled Model'}
                </h2>
                <div className="flex items-center mt-2 space-x-4">
                  <span className="bg-gray-200 dark:bg-gray-700 px-3 py-1 rounded-full text-sm">
                    {formData.category || 'No Category'}
                  </span>
                  {formData.subcategory && (
                    <span className="bg-gray-200 dark:bg-gray-700 px-3 py-1 rounded-full text-sm">
                      {formData.subcategory}
                    </span>
                  )}
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">
                    {formData.format.toUpperCase()}
                  </span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">
                  {formData.price > 0 ? `$${formData.price}` : 'Free'}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Status: {formData.status}
                </div>
              </div>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {formData.description || 'No description provided.'}
            </p>

            {formData.tags.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags:</h4>
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Format:</span>
                <p className="text-gray-600 dark:text-gray-400">{formData.format.toUpperCase()}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">File Size:</span>
                <p className="text-gray-600 dark:text-gray-400">{formData.fileSize || 'N/A'}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Polygons:</span>
                <p className="text-gray-600 dark:text-gray-400">{formData.specifications.polygons || 'N/A'}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Vertices:</span>
                <p className="text-gray-600 dark:text-gray-400">{formData.specifications.vertices || 'N/A'}</p>
              </div>
            </div>
          </div>
        </motion.div>
      ) : (
        // Edit Mode - Form
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex items-center mb-4">
              <FiInfo className="h-5 w-5 text-blue-600 mr-2" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Model Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="Enter model title"
                />
                {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="Describe the model..."
                />
                {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category *
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.category ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                >
                  <option value="">Select Category</option>
                  {Object.keys(categories).map(cat => (
                    <option key={cat} value={cat}>
                      {cat.charAt(0).toUpperCase() + cat.slice(1)}
                    </option>
                  ))}
                </select>
                {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subcategory
                </label>
                <select
                  name="subcategory"
                  value={formData.subcategory}
                  onChange={handleChange}
                  disabled={!formData.category}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
                >
                  <option value="">Select Subcategory</option>
                  {formData.category && categories[formData.category]?.map(subcat => (
                    <option key={subcat} value={subcat}>
                      {subcat}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </motion.div>

          {/* File URLs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex items-center mb-4">
              <FiFile className="h-5 w-5 text-green-600 mr-2" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">File URLs</h2>
            </div>

            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  File URL *
                </label>
                <input
                  type="url"
                  name="fileUrl"
                  value={formData.fileUrl}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.fileUrl ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="https://example.com/model.skp"
                />
                {errors.fileUrl && <p className="text-red-500 text-xs mt-1">{errors.fileUrl}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Preview Image URL *
                </label>
                <input
                  type="url"
                  name="imageUrl"
                  value={formData.imageUrl}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.imageUrl ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="https://example.com/preview.jpg"
                />
                {errors.imageUrl && <p className="text-red-500 text-xs mt-1">{errors.imageUrl}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  3D Model URL (GLB/GLTF for preview)
                </label>
                <input
                  type="url"
                  name="modelUrl"
                  value={formData.modelUrl}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="https://example.com/model.glb"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Optional: URL to GLB/GLTF file for 3D preview
                </p>
              </div>
            </div>
          </motion.div>

          {/* Tags and Pricing */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex items-center mb-4">
              <FiTag className="h-5 w-5 text-purple-600 mr-2" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Tags & Pricing</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tags
                </label>
                <div className="space-y-2">
                  <div className="flex">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleTagInput}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter tags (press Enter or comma to add)"
                    />
                    <button
                      type="button"
                      onClick={addTag}
                      className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      Add
                    </button>
                  </div>
                  {formData.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                          >
                            <FiX className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  File Size (bytes)
                </label>
                <input
                  type="number"
                  name="fileSize"
                  value={formData.fileSize}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="15925248"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  File size in bytes (e.g., 15925248 for 15.2 MB)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Software Format *
                </label>
                <select
                  name="format"
                  value={formData.format}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.format ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                >
                  {formats.map(format => (
                    <option key={format} value={format}>
                      {format}
                    </option>
                  ))}
                </select>
                {errors.format && <p className="text-red-500 text-xs mt-1">{errors.format}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  File Format *
                </label>
                <select
                  name="fileFormat"
                  value={formData.fileFormat}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.fileFormat ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                >
                  {fileFormats.map(format => (
                    <option key={format} value={format}>
                      {format.toUpperCase()}
                    </option>
                  ))}
                </select>
                {errors.fileFormat && <p className="text-red-500 text-xs mt-1">{errors.fileFormat}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Year
                </label>
                <input
                  type="text"
                  name="year"
                  value={formData.year}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="2023"
                />
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="isPremium"
                  checked={formData.isPremium}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Premium Model</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="textured"
                  checked={formData.textured}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Textured</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="rigged"
                  checked={formData.rigged}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Rigged</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="animated"
                  checked={formData.animated}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Animated</span>
              </label>
            </div>
          </motion.div>

          {/* Technical Specifications */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex items-center mb-4">
              <FiSettings className="h-5 w-5 text-orange-600 mr-2" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Technical Specifications</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Polygon Count
                </label>
                <input
                  type="number"
                  name="polygonCount"
                  value={formData.polygonCount}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="125000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Width
                </label>
                <input
                  type="number"
                  name="dimensions.width"
                  value={formData.dimensions.width}
                  onChange={handleChange}
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="4.5"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Height
                </label>
                <input
                  type="number"
                  name="dimensions.height"
                  value={formData.dimensions.height}
                  onChange={handleChange}
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="3.2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Depth
                </label>
                <input
                  type="number"
                  name="dimensions.depth"
                  value={formData.dimensions.depth}
                  onChange={handleChange}
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="5.8"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Unit
                </label>
                <select
                  name="dimensions.unit"
                  value={formData.dimensions.unit}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {units.map(unit => (
                    <option key={unit} value={unit}>
                      {unit}
                    </option>
                  ))}
                </select>
              </div>
            </div>

          </motion.div>

          {/* Submit Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex justify-end space-x-4"
          >
            <button
              type="button"
              onClick={() => navigate('/admin/models')}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <FiSave className="h-4 w-4 mr-2" />
                  Create Model
                </>
              )}
            </button>
          </motion.div>
        </form>
      )}
    </div>
  );
};

export default AddModel;
