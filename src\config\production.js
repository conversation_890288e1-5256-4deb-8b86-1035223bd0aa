// Production configuration
export const isProd = process.env.NODE_ENV === 'production';
export const isDev = process.env.NODE_ENV === 'development';

// Performance optimization settings
export const PERFORMANCE_CONFIG = {
  // Cache settings
  CACHE_DURATION: isProd ? 600000 : 60000, // 10 min prod, 1 min dev
  MAX_CACHE_SIZE: 100,

  // API settings
  REQUEST_TIMEOUT: 10000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,

  // UI settings
  ANIMATION_DURATION: isProd ? 200 : 300, // Faster animations in prod
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,

  // Image optimization
  IMAGE_QUALITY: isProd ? 80 : 90,
  LAZY_LOAD_THRESHOLD: 100,

  // Bundle optimization
  CHUNK_SIZE_LIMIT: 244000, // 244KB
  PRELOAD_CRITICAL: true,

  // Memory management
  MAX_COMPONENTS_CACHE: 50,
  CLEANUP_INTERVAL: 300000, // 5 minutes
};

// Disable console logs in production
if (isProd) {
  console.log = () => {};
  console.warn = () => {};
  console.info = () => {};
  console.debug = () => {};
  // Keep console.error for critical errors
}

// Performance monitoring
export const MONITORING = {
  ENABLE_PERFORMANCE_TRACKING: true,
  ENABLE_ERROR_TRACKING: true,
  ENABLE_USER_ANALYTICS: isProd,
  SAMPLE_RATE: isProd ? 0.1 : 1.0, // 10% sampling in prod
};

export default {
  isProd,
  isDev,
  enableLogs: !isProd,
  enableDebug: !isProd,
  performance: PERFORMANCE_CONFIG,
  monitoring: MONITORING
};