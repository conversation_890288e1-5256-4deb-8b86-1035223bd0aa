import { useState, useEffect, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiMonitor, FiCpu, FiActivity, FiX, FiRefreshCw } from 'react-icons/fi';
import { getPerformanceSummary, getDetailedMetrics } from '../../utils/performanceMonitor';
import { useMemoryMonitor } from '../../hooks/useOptimizedPerformance';

const PerformancePanel = memo(() => {
  const [isOpen, setIsOpen] = useState(false);
  const [metrics, setMetrics] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const memoryInfo = useMemoryMonitor();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const refreshMetrics = async () => {
    setIsRefreshing(true);
    try {
      const summary = getPerformanceSummary();
      const detailed = getDetailedMetrics();
      setMetrics({ summary, detailed });
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      refreshMetrics();
      const interval = setInterval(refreshMetrics, 5000); // Refresh every 5 seconds
      return () => clearInterval(interval);
    }
  }, [isOpen]);

  const formatTime = (time) => {
    if (!time) return 'N/A';
    return `${time.toFixed(2)}ms`;
  };

  const formatMemory = (bytes) => {
    if (!bytes) return 'N/A';
    return `${bytes}MB`;
  };

  const getPerformanceColor = (value, thresholds) => {
    if (value < thresholds.good) return 'text-green-500';
    if (value < thresholds.warning) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Performance Monitor"
      >
        <FiMonitor className="w-5 h-5" />
      </motion.button>

      {/* Performance Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            className="fixed top-4 right-4 z-40 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 max-h-[80vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <FiActivity className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900 dark:text-white">Performance Monitor</h3>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={refreshMetrics}
                  disabled={isRefreshing}
                  className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                  title="Refresh Metrics"
                >
                  <FiRefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-4 space-y-4">
              {/* Memory Usage */}
              {memoryInfo && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                    <FiCpu className="w-4 h-4" />
                    Memory Usage
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Used:</span>
                      <span className={getPerformanceColor(memoryInfo.used, { good: 50, warning: 100 })}>
                        {formatMemory(memoryInfo.used)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Total:</span>
                      <span className="text-gray-900 dark:text-white">{formatMemory(memoryInfo.total)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Limit:</span>
                      <span className="text-gray-900 dark:text-white">{formatMemory(memoryInfo.limit)}</span>
                    </div>
                    {/* Memory Usage Bar */}
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          memoryInfo.used / memoryInfo.limit > 0.8 ? 'bg-red-500' :
                          memoryInfo.used / memoryInfo.limit > 0.6 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min((memoryInfo.used / memoryInfo.limit) * 100, 100)}%` }}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Performance Metrics */}
              {metrics?.summary && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Performance Metrics</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Page Load:</span>
                      <span className={getPerformanceColor(metrics.summary.pageLoadTime, { good: 1000, warning: 3000 })}>
                        {formatTime(metrics.summary.pageLoadTime)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">First Paint:</span>
                      <span className={getPerformanceColor(metrics.summary.renderTime, { good: 1000, warning: 2000 })}>
                        {formatTime(metrics.summary.renderTime)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">API Calls:</span>
                      <span className="text-gray-900 dark:text-white">{metrics.summary.apiCallsCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Components:</span>
                      <span className="text-gray-900 dark:text-white">{metrics.summary.componentRenderCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Errors:</span>
                      <span className={metrics.summary.errorCount > 0 ? 'text-red-500' : 'text-green-500'}>
                        {metrics.summary.errorCount}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Interactions:</span>
                      <span className="text-gray-900 dark:text-white">{metrics.summary.userInteractions}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Timing */}
              {metrics?.summary?.navigationTiming && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Navigation Timing</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">DNS:</span>
                      <span className="text-gray-900 dark:text-white">
                        {formatTime(metrics.summary.navigationTiming.dns)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">TCP:</span>
                      <span className="text-gray-900 dark:text-white">
                        {formatTime(metrics.summary.navigationTiming.tcp)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Request:</span>
                      <span className="text-gray-900 dark:text-white">
                        {formatTime(metrics.summary.navigationTiming.request)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Response:</span>
                      <span className="text-gray-900 dark:text-white">
                        {formatTime(metrics.summary.navigationTiming.response)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">DOM:</span>
                      <span className="text-gray-900 dark:text-white">
                        {formatTime(metrics.summary.navigationTiming.dom)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Connection Info */}
              {metrics?.detailed?.connection && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Connection</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Type:</span>
                      <span className="text-gray-900 dark:text-white">
                        {metrics.detailed.connection.effectiveType}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Downlink:</span>
                      <span className="text-gray-900 dark:text-white">
                        {metrics.detailed.connection.downlink} Mbps
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">RTT:</span>
                      <span className="text-gray-900 dark:text-white">
                        {metrics.detailed.connection.rtt}ms
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
});

PerformancePanel.displayName = 'PerformancePanel';

export default PerformancePanel;
