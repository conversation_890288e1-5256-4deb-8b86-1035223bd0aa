import React, { useState, useEffect, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import {
  FiDownload,
  FiUsers,
  FiBox,
  FiGrid,
  FiRefreshCw,
  FiTrendingUp,
  FiBarChart2,
  FiPieChart
} from 'react-icons/fi';
import { useModels } from '../context/ModelContext';
import realDataService from '../services/realDataService';
import LoadingIndicator from './ui/LoadingIndicator';

/**
 * Statistics Display Component
 * Shows real-time statistics from MongoDB
 */
const ComponentName = memo((props) => {
  const [statistics, setStatistics] = useState({
    modelsCount: 0,
    usersCount: 0,
    downloadsCount: 0,
    categoriesCount: 0
  });
  const [loading, setLoading] = useState(!stats);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Fetch statistics using realDataService
  const fetchStatistics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const fetchedStats = await realDataService.getStats();
      setStatistics({
        modelsCount: fetchedStats.totalModels || fetchedStats.models || 0,
        usersCount: fetchedStats.totalUsers || fetchedStats.users || 0,
        downloadsCount: fetchedStats.totalDownloads || fetchedStats.downloads || 0,
        categoriesCount: fetchedStats.categories || 0
      });

      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to load statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  // Use passed stats or fetch if not provided
  useEffect(() => {
    if (stats) {
      setStatistics({
        modelsCount: stats.totalModels || stats.models || 0,
        usersCount: stats.totalUsers || stats.users || 0,
        downloadsCount: stats.totalDownloads || stats.downloads || 0,
        categoriesCount: stats.categories || 0
      });
      setLoading(false);
      setLastUpdated(new Date());
    } else {
      fetchStatistics();
    }
  }, [stats, fetchStatistics]);

  // Format number with commas
  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  // Format last updated time
  const formatLastUpdated = () => {
    if (!lastUpdated) return '';

    return lastUpdated.toLocaleTimeString([], {
    hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
    opacity: 1,
      transition: {
    staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
    y: 0,
      opacity: 1,
      transition: {
    type: 'spring',
        stiffness: 300,
        damping: 24
      }
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
        <LoadingIndicator size="md" text="Loading statistics..." />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center ${className}`}>
        <p className="text-red-500 dark:text-red-400 mb-2">{error}</p>
        <button
          onClick={fetchStatistics}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <FiRefreshCw className="inline mr-2" />
          Retry
        </button>
      </div>
    );
  }

  return (
    <motion.div
      className={`text-center ${className}`}
      initial={animateOnMount ? 'hidden' : 'visible'}
      animate="visible"
      variants={containerVariants}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="mb-16"
      >
        <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm text-white/90 rounded-full text-sm font-medium mb-4 border border-white/30">
          📊 Real-time Statistics
        </span>
        <h2 className="text-4xl md:text-5xl font-black text-white mb-6">
          Thống Kê Thực Tế
        </h2>
        <p className="text-xl text-white/90 max-w-3xl mx-auto">
          Dữ liệu thống kê trực tiếp từ cơ sở dữ liệu MongoDB của chúng tôi
        </p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto"
      >
        {/* Models count */}
        <motion.div
          variants={itemVariants}
          className="group relative p-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-2xl"
        >
          <div className="text-center">
            <div className="inline-flex p-4 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <FiBox className="w-8 h-8 text-white" />
            </div>
            <div className="text-4xl md:text-5xl font-black text-white mb-2">
              {formatNumber(statistics.modelsCount)}
            </div>
            <div className="text-white/80 text-sm font-medium">3D Models</div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-cyan-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </motion.div>

        {/* Users count */}
        <motion.div
          variants={itemVariants}
          className="group relative p-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-2xl"
        >
          <div className="text-center">
            <div className="inline-flex p-4 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <FiUsers className="w-8 h-8 text-white" />
            </div>
            <div className="text-4xl md:text-5xl font-black text-white mb-2">
              {formatNumber(statistics.usersCount)}
            </div>
            <div className="text-white/80 text-sm font-medium">Active Users</div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-green-400/10 to-emerald-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </motion.div>

        {/* Downloads count */}
        <motion.div
          variants={itemVariants}
          className="group relative p-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-2xl"
        >
          <div className="text-center">
            <div className="inline-flex p-4 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <FiDownload className="w-8 h-8 text-white" />
            </div>
            <div className="text-4xl md:text-5xl font-black text-white mb-2">
              {formatNumber(statistics.downloadsCount)}
            </div>
            <div className="text-white/80 text-sm font-medium">Downloads</div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-purple-400/10 to-pink-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </motion.div>

        {/* Categories count */}
        <motion.div
          variants={itemVariants}
          className="group relative p-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-2xl"
        >
          <div className="text-center">
            <div className="inline-flex p-4 bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <FiGrid className="w-8 h-8 text-white" />
            </div>
            <div className="text-4xl md:text-5xl font-black text-white mb-2">
              {formatNumber(statistics.categoriesCount)}
            </div>
            <div className="text-white/80 text-sm font-medium">Categories</div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-orange-400/10 to-red-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </motion.div>
      </motion.div>

    </motion.div>
  );
};

export default memo(StatisticsDisplay);
