import { PERFORMANCE_CONFIG, MONITORING } from '../config/production';

/**
 * Performance monitoring utility
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      renderTime: 0,
      apiCallTimes: new Map(),
      memoryUsage: [],
      componentRenderCounts: new Map(),
      errorCount: 0,
      userInteractions: 0
    };

    this.observers = {
      performance: null,
      memory: null,
      navigation: null
    };

    this.isEnabled = MONITORING.ENABLE_PERFORMANCE_TRACKING;
    this.sampleRate = MONITORING.SAMPLE_RATE;

    if (this.isEnabled && Math.random() < this.sampleRate) {
      this.init();
    }
  }

  init() {
    this.setupPerformanceObserver();
    this.setupMemoryMonitoring();
    this.setupNavigationTiming();
    this.setupErrorTracking();
    this.setupUserInteractionTracking();
  }

  setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      this.observers.performance = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach(entry => {
          switch (entry.entryType) {
            case 'navigation':
              this.metrics.pageLoadTime = entry.loadEventEnd - entry.loadEventStart;
              break;
            case 'paint':
              if (entry.name === 'first-contentful-paint') {
                this.metrics.renderTime = entry.startTime;
              }
              break;
            case 'measure':
              if (entry.name.startsWith('api-call-')) {
                this.metrics.apiCallTimes.set(entry.name, entry.duration);
              }
              break;
          }
        });
      });

      try {
        this.observers.performance.observe({ 
          entryTypes: ['navigation', 'paint', 'measure', 'resource'] 
        });
      } catch (error) {
        // Fallback for older browsers
        this.observers.performance.observe({ entryTypes: ['navigation'] });
      }
    }
  }

  setupMemoryMonitoring() {
    if ('memory' in performance) {
      const checkMemory = () => {
        const memInfo = {
          used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576), // MB
          timestamp: Date.now()
        };

        this.metrics.memoryUsage.push(memInfo);

        // Keep only last 50 measurements
        if (this.metrics.memoryUsage.length > 50) {
          this.metrics.memoryUsage.shift();
        }

        // Warn if memory usage is high
        if (memInfo.used > memInfo.limit * 0.8) {
          console.warn('High memory usage detected:', memInfo);
        }
      };

      checkMemory();
      setInterval(checkMemory, 30000); // Check every 30 seconds
    }
  }

  setupNavigationTiming() {
    if ('getEntriesByType' in performance) {
      const navEntries = performance.getEntriesByType('navigation');
      if (navEntries.length > 0) {
        const nav = navEntries[0];
        this.metrics.navigationTiming = {
          dns: nav.domainLookupEnd - nav.domainLookupStart,
          tcp: nav.connectEnd - nav.connectStart,
          request: nav.responseStart - nav.requestStart,
          response: nav.responseEnd - nav.responseStart,
          dom: nav.domContentLoadedEventEnd - nav.domContentLoadedEventStart,
          load: nav.loadEventEnd - nav.loadEventStart
        };
      }
    }
  }

  setupErrorTracking() {
    if (MONITORING.ENABLE_ERROR_TRACKING) {
      window.addEventListener('error', (event) => {
        this.metrics.errorCount++;
        this.logError('JavaScript Error', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        });
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.metrics.errorCount++;
        this.logError('Unhandled Promise Rejection', {
          reason: event.reason,
          promise: event.promise
        });
      });
    }
  }

  setupUserInteractionTracking() {
    const interactionEvents = ['click', 'scroll', 'keydown', 'touchstart'];
    
    interactionEvents.forEach(eventType => {
      document.addEventListener(eventType, () => {
        this.metrics.userInteractions++;
      }, { passive: true });
    });
  }

  // Track API call performance
  trackAPICall(name, startTime, endTime, success = true) {
    if (!this.isEnabled) return;

    const duration = endTime - startTime;
    const key = `api-${name}`;
    
    if (!this.metrics.apiCallTimes.has(key)) {
      this.metrics.apiCallTimes.set(key, []);
    }
    
    this.metrics.apiCallTimes.get(key).push({
      duration,
      success,
      timestamp: Date.now()
    });

    // Performance mark for PerformanceObserver
    if ('performance' in window && 'mark' in performance) {
      performance.mark(`api-call-${name}-start`);
      performance.mark(`api-call-${name}-end`);
      performance.measure(`api-call-${name}`, `api-call-${name}-start`, `api-call-${name}-end`);
    }
  }

  // Track component render performance
  trackComponentRender(componentName, renderTime) {
    if (!this.isEnabled) return;

    if (!this.metrics.componentRenderCounts.has(componentName)) {
      this.metrics.componentRenderCounts.set(componentName, {
        count: 0,
        totalTime: 0,
        averageTime: 0,
        maxTime: 0
      });
    }

    const stats = this.metrics.componentRenderCounts.get(componentName);
    stats.count++;
    stats.totalTime += renderTime;
    stats.averageTime = stats.totalTime / stats.count;
    stats.maxTime = Math.max(stats.maxTime, renderTime);

    // Warn about slow renders
    if (renderTime > 16) { // More than one frame
      console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }
  }

  // Log performance error
  logError(type, details) {
    if (process.env.NODE_ENV === 'development') {
      console.error(`[Performance Monitor] ${type}:`, details);
    }

    // In production, you might want to send this to an analytics service
    if (MONITORING.ENABLE_ERROR_TRACKING && process.env.NODE_ENV === 'production') {
      // Send to analytics service
      this.sendToAnalytics('error', { type, details, timestamp: Date.now() });
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    return {
      pageLoadTime: this.metrics.pageLoadTime,
      renderTime: this.metrics.renderTime,
      memoryUsage: this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1],
      apiCallsCount: this.metrics.apiCallTimes.size,
      componentRenderCount: this.metrics.componentRenderCounts.size,
      errorCount: this.metrics.errorCount,
      userInteractions: this.metrics.userInteractions,
      navigationTiming: this.metrics.navigationTiming
    };
  }

  // Get detailed metrics
  getDetailedMetrics() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null
    };
  }

  // Send data to analytics service
  sendToAnalytics(eventType, data) {
    // Implement your analytics service integration here
    // Example: Google Analytics, Mixpanel, etc.
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Analytics] ${eventType}:`, data);
    }
  }

  // Clean up observers
  cleanup() {
    Object.values(this.observers).forEach(observer => {
      if (observer && observer.disconnect) {
        observer.disconnect();
      }
    });
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Export utility functions
export const trackAPICall = (name, startTime, endTime, success) => {
  performanceMonitor.trackAPICall(name, startTime, endTime, success);
};

export const trackComponentRender = (componentName, renderTime) => {
  performanceMonitor.trackComponentRender(componentName, renderTime);
};

export const getPerformanceSummary = () => {
  return performanceMonitor.getPerformanceSummary();
};

export const getDetailedMetrics = () => {
  return performanceMonitor.getDetailedMetrics();
};

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  performanceMonitor.cleanup();
});

export default performanceMonitor;
