#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

const filePath = 'src/components/Chatbot.jsx';

console.log('🔧 Fixing Chatbot.jsx syntax errors...');

try {
  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;

  // Fix common indentation issues
  const fixes = [
    // Fix missing spaces after function declarations
    { pattern: /const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{\s*([A-Za-z])/g, replacement: 'const $1 = ($2) => {\n    $3' },
    
    // Fix indentation for try blocks
    { pattern: /(\s*)try\s*\{\s*([A-Za-z])/g, replacement: '$1try {\n$1  $2' },
    
    // Fix indentation for if statements
    { pattern: /(\s*)if\s*\([^)]+\)\s*\{\s*([A-Za-z])/g, replacement: '$1if ($2) {\n$1  $3' },
    
    // Fix function calls with wrong indentation
    { pattern: /^(\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\(/gm, replacement: '    $2(' },
    
    // Fix object properties with wrong indentation
    { pattern: /^(\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/gm, replacement: '      $2:' },
    
    // Fix closing braces
    { pattern: /^(\s*)\}\s*;?\s*$/gm, replacement: '  };' },
    
    // Fix useCallback dependencies
    { pattern: /\}, \[\]\);/g, replacement: '  }, []);' },
    { pattern: /\}, \[([^\]]+)\]\);/g, replacement: '  }, [$1]);' },
    
    // Fix specific problematic lines
    { pattern: /^(\s*)- no limits`\);/gm, replacement: '    // Retry with no limits' },
    { pattern: /^(\s*)if \(false\) \{/gm, replacement: '    if (response.intent === "processing") {' },
    { pattern: /^(\s*)setTimeout\(handleRetry, 1000\);/gm, replacement: '      setTimeout(handleRetry, 1000);' },
    { pattern: /^(\s*)return;/gm, replacement: '      return;' },
    
    // Fix specific syntax errors
    { pattern: /const handleSuggestionClick = useCallback\(\(suggestion\) => \{\s*setInputValue\(suggestion\);\s*handleSendMessage\(\);\s*\};/g, 
      replacement: 'const handleSuggestionClick = useCallback((suggestion) => {\n    setInputValue(suggestion);\n    handleSendMessage();\n  }, []);' },
    
    // Fix checkConnection function
    { pattern: /const checkConnection = async \(\) => \{\s*try \{/g, replacement: 'const checkConnection = async () => {\n      try {' },
    { pattern: /\} catch \(error\) \{\s*setIsConnected\(false\);/g, replacement: '      } catch (error) {\n        setIsConnected(false);' },
    
    // Fix getGeminiResponse function
    { pattern: /const getGeminiResponse = async \(userMessage\) => \{/g, replacement: 'const getGeminiResponse = async (userMessage) => {' },
    
    // Fix handleSendMessage function
    { pattern: /const handleSendMessage = async \(e\) => \{\s*e\?\.\preventDefault\(\);/g, 
      replacement: 'const handleSendMessage = async (e) => {\n    e?.preventDefault();' },
    
    // Fix object literals
    { pattern: /\{\s*id: Date\.now\(\),/g, replacement: '{\n      id: Date.now(),' },
    { pattern: /text: ([^,]+),/g, replacement: '      text: $1,' },
    { pattern: /sender: '([^']+)',/g, replacement: '      sender: \'$1\',' },
    { pattern: /timestamp: new Date\(\),?/g, replacement: '      timestamp: new Date(),' },
    { pattern: /intent: '([^']+)',?/g, replacement: '      intent: \'$1\',' },
  ];

  // Apply fixes
  fixes.forEach(fix => {
    const newContent = content.replace(fix.pattern, fix.replacement);
    if (newContent !== content) {
      changes++;
      content = newContent;
    }
  });

  // Clean up multiple empty lines
  content = content.replace(/\n\s*\n\s*\n+/g, '\n\n');

  // Fix specific problematic sections manually
  content = content.replace(
    /- no limits`\);/g,
    '// Retry with no limits'
  );

  content = content.replace(
    /if \(false\) \{[^}]*\}/g,
    'if (response.intent === "processing") {\n      setTimeout(handleRetry, 1000);\n      return;\n    }'
  );

  // Write the fixed content back
  fs.writeFileSync(filePath, content, 'utf8');

  console.log(`✅ Fixed ${changes} syntax issues in Chatbot.jsx`);
  console.log('🎉 Chatbot.jsx syntax has been cleaned up!');

} catch (error) {
  console.error('❌ Error fixing Chatbot.jsx:', error.message);
  process.exit(1);
}
