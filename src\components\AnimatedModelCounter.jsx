import { useState, useEffect, useRef, memo } from 'react';
import { motion } from 'framer-motion';
import { FiDatabase } from 'react-icons/fi';
import { PERFORMANCE_CONFIG } from '../config/production';

const AnimatedModelCounter = memo(({
  end = 0,
  duration = PERFORMANCE_CONFIG.ANIMATION_DURATION * 10, // Use config duration
  className = ""
}) => {
  const [count, setCount] = useState(0);
  const animationRef = useRef(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    // Reset mounted flag
    mountedRef.current = true;

    const startTime = Date.now();
    const startValue = 0;
    const difference = end - startValue;

    const animate = () => {
      // Check if component is still mounted
      if (!mountedRef.current) return;

      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = Math.floor(startValue + difference * easeOutQuart);

      setCount(current);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    // Cleanup function
    return () => {
      mountedRef.current = false;
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [end, duration]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6 }}
      className={`text-3xl md:text-4xl font-black text-white ${className}`}
    >
      {count.toLocaleString()}
    </motion.div>
  );
});

// Compact version for smaller spaces
export const CompactModelCounter = memo(({ models }) => {
  const [count, setCount] = useState(0);
  const animationRef = useRef(null);
  const mountedRef = useRef(true);
  const targetCount = models?.length || 2847;

  useEffect(() => {
    mountedRef.current = true;
    const duration = PERFORMANCE_CONFIG.ANIMATION_DURATION * 8;
    const startTime = Date.now();

    const animate = () => {
      if (!mountedRef.current) return;

      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeOut = 1 - Math.pow(1 - progress, 3);

      setCount(Math.floor(targetCount * easeOut));

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      mountedRef.current = false;
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [targetCount]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6 }}
      className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg"
    >
      <FiDatabase className="w-4 h-4" />
      <span className="font-bold text-lg">
        {count.toLocaleString()}+
      </span>
      <span className="text-sm opacity-90">Models</span>
    </motion.div>
  );
});

export default AnimatedModelCounter;
