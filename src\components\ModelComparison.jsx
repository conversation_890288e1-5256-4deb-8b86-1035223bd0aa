import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiX, FiStar, FiDownload, FiEye, FiHeart, <PERSON>Check, FiMinus } from 'react-icons/fi';

const ComponentName = memo((props) => {
  const [selectedFeatures, setSelectedFeatures] = useState([
    'title',
    'category',
    'rating',
    'downloads',
    'isPremium',
    'format',
    'fileSize'
  ]);

  const features = [
    { key: 'title', label: 'Tên Model', icon: FiEye },
    { key: 'category', label: '<PERSON><PERSON>', icon: FiEye },
    { key: 'rating', label: '<PERSON><PERSON><PERSON>', icon: FiStar },
    { key: 'downloads', label: '<PERSON><PERSON><PERSON><PERSON>ả<PERSON>', icon: FiDownload },
    { key: 'isPremium', label: 'Loại', icon: FiHeart },
    { key: 'format', label: 'Định <PERSON>ng', icon: FiEye },
    { key: 'fileSize', label: '<PERSON><PERSON><PERSON>', icon: FiEye },
    { key: 'description', label: '<PERSON><PERSON>ả', icon: FiEye }
  ];

  const toggleFeature = (featureKey) => {
    setSelectedFeatures(prev => 
      prev.includes(featureKey)
        ? prev.filter(f => f !== featureKey)
        : [...prev, featureKey]
    );
  };

  const renderFeatureValue = (model, featureKey) => {
    switch (featureKey) {
      case 'title':
        return (
          <div className="font-semibold text-gray-900 dark:text-white">
            {model.title}
          </div>
        );
      case 'category':
        return (
          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-sm">
            {model.category}
          </span>
        );
      case 'rating':
        return (
          <div className="flex items-center">
            <FiStar className="h-4 w-4 text-yellow-500 mr-1" />
            <span className="font-medium">{model.rating}</span>
          </div>
        );
      case 'downloads':
        return (
          <div className="flex items-center">
            <FiDownload className="h-4 w-4 text-green-500 mr-1" />
            <span>{model.downloads?.toLocaleString() || 0}</span>
          </div>
        );
      case 'isPremium':
        return (
          <span className={`px-2 py-1 rounded-lg text-sm font-medium ${
            model.isPremium
              ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white'
              : 'bg-gradient-to-r from-green-400 to-blue-500 text-white'
          }`}>
            {model.isPremium ? '⭐ Premium' : '🎉 Free'}
          </span>
        );
      case 'format':
        return (
          <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-lg text-sm">
            {model.format || 'SKP'}
          </span>
        );
      case 'fileSize':
        return (
          <span className="text-gray-600 dark:text-gray-400">
            {model.fileSize || 'N/A'}
          </span>
        );
      case 'description':
        return (
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
            {model.description}
          </p>
        );
      default:
        return model[featureKey] || 'N/A';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ duration: 0.3 }}
          className="glass-card rounded-3xl border border-white/20 shadow-professional-lg max-w-6xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-black text-gray-900 dark:text-white mb-2">
                  So Sánh Models
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  So sánh {models.length} models được chọn
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-colors"
              >
                <FiX className="h-6 w-6 text-gray-500" />
              </button>
            </div>
          </div>

          {/* Feature Toggle */}
          <div className="p-6 border-b border-white/10">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              Chọn thông tin so sánh:
            </h3>
            <div className="flex flex-wrap gap-2">
              {features.map((feature) => (
                <button
                  key={feature.key}
                  onClick={() => toggleFeature(feature.key)}
                  className={`flex items-center px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                    selectedFeatures.includes(feature.key)
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                      : 'glass-card border border-white/10 text-gray-700 dark:text-gray-300 hover:bg-white/20'
                  }`}
                >
                  <feature.icon className="h-4 w-4 mr-2" />
                  {feature.label}
                  {selectedFeatures.includes(feature.key) ? (
                    <FiCheck className="h-4 w-4 ml-2" />
                  ) : (
                    <FiMinus className="h-4 w-4 ml-2 opacity-50" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Comparison Table */}
          <div className="flex-1 overflow-auto">
            <div className="p-6">
              <div className="grid gap-6" style={{ gridTemplateColumns: `repeat(${models.length}, 1fr)` }}>
                {models.map((model, index) => (
                  <motion.div
                    key={model.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="glass-card rounded-2xl border border-white/10 overflow-hidden"
                  >
                    {/* Model Image */}
                    <div className="relative h-48 bg-gray-100 dark:bg-gray-800">
                      <img
                        src={model.imageUrl}
                        alt={model.title}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => onRemoveModel(model.id)}
                        className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                      >
                        <FiX className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Model Details */}
                    <div className="p-4 space-y-4">
                      {selectedFeatures.map((featureKey) => (
                        <div key={featureKey} className="border-b border-white/10 pb-3 last:border-b-0 last:pb-0">
                          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1 uppercase tracking-wide">
                            {features.find(f => f.key === featureKey)?.label}
                          </div>
                          {renderFeatureValue(model, featureKey)}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-white/10 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                💡 Tip: Click vào tên tính năng để bật/tắt hiển thị
              </p>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-300"
              >
                Đóng
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ModelComparison;
