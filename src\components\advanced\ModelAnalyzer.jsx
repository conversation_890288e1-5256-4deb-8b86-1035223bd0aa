import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Html } from '@react-three/drei';
import { FiX, FiInfo, FiCpu, FiBox, FiLayers, FiImage } from 'react-icons/fi';
import * as THREE from 'three';

/**
 * ModelAnalyzer component for analyzing 3D model statistics
 *
 * @param {Object} props - Component props
 * @param {Object} props.modelRef - Reference to the model object
 * @param {boolean} props.visible - Whether the analyzer is visible
 * @param {Function} props.onClose - Function to call when closing the analyzer
 */
const ComponentName = memo((props) => {
  const [stats, setStats] = useState({
    vertices: 0,
    faces: 0,
    triangles: 0,
    materials: 0,
    textures: 0,
    objects: 0,
    meshes: 0,
    animations: 0,
    bones: 0,
    size: {
      width: 0,
      height: 0,
      depth: 0,
    },
    memoryUsage: 0,
  });

  // Analyze the model when it changes or becomes visible
  useEffect(() => {
    if (!visible || !modelRef.current) return;

    const analyzeModel = () => {
      let vertices = 0;
      let faces = 0;
      let triangles = 0;
      let materials = new Set();
      let textures = new Set();
      let objects = 0;
      let meshes = 0;
      let animations = 0;
      let bones = 0;
      let memoryUsage = 0;

      // Create a box to calculate model dimensions
      const box = new THREE.Box3();

      // Traverse the model to collect statistics
      modelRef.current.traverse((object) => {
        objects++;

        if (object.isMesh) {
          meshes++;

          // Count vertices and faces
          if (object.geometry) {
            if (object.geometry.attributes && object.geometry.attributes.position) {
              vertices += object.geometry.attributes.position.count;
            }

            if (object.geometry.index) {
              triangles += object.geometry.index.count / 3;
              faces += object.geometry.index.count / 3;
            } else if (object.geometry.attributes.position) {
              triangles += object.geometry.attributes.position.count / 3;
              faces += object.geometry.attributes.position.count / 3;
            }

            // Estimate memory usage
            if (object.geometry.attributes) {
              Object.values(object.geometry.attributes).forEach(attribute => {
                if (attribute.array) {
                  memoryUsage += attribute.array.byteLength;
                }
              });
            }

            if (object.geometry.index && object.geometry.index.array) {
              memoryUsage += object.geometry.index.array.byteLength;
            }
          }

          // Count materials and textures
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(mat => {
                materials.add(mat);

                // Check for textures in the material
                Object.values(mat).forEach(value => {
                  if (value && value.isTexture) {
                    textures.add(value);

                    // Estimate texture memory usage
                    if (value.image) {
                      const width = value.image.width || 0;
                      const height = value.image.height || 0;
                      // Estimate 4 bytes per pixel (RGBA)
                      memoryUsage += width * height * 4;
                    }
                  }
                });
              });
            } else {
              materials.add(object.material);

              // Check for textures in the material
              Object.values(object.material).forEach(value => {
                if (value && value.isTexture) {
                  textures.add(value);

                  // Estimate texture memory usage
                  if (value.image) {
                    const width = value.image.width || 0;
                    const height = value.image.height || 0;
                    // Estimate 4 bytes per pixel (RGBA)
                    memoryUsage += width * height * 4;
                  }
                }
              });
            }
          }

          // Update bounding box
          if (object.geometry) {
            object.geometry.computeBoundingBox();
            box.expandByObject(object);
          }
        }

        // Count bones
        if (object.isBone) {
          bones++;
        }
      });

      // Count animations
      if (modelRef.current.animations) {
        animations = modelRef.current.animations.length;
      }

      // Calculate model dimensions
      const size = new THREE.Vector3();
      box.getSize(size);

      // Convert memory usage to MB
      const memoryUsageMB = memoryUsage / (1024 * 1024);

      // Update stats
      setStats({
        vertices,
        faces,
        triangles,
        materials: materials.size,
        textures: textures.size,
        objects,
        meshes,
        animations,
        bones,
        size: {
          width: parseFloat(size.x.toFixed(2)),
          height: parseFloat(size.y.toFixed(2)),
          depth: parseFloat(size.z.toFixed(2)),
        },
        memoryUsage: parseFloat(memoryUsageMB.toFixed(2)),
      });
    };

    // Run analysis
    analyzeModel();
  }, [visible, modelRef]);

  if (!visible) return null;

  return (
    <Html position={[0, 0, 0]} center portal>
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-80 max-h-96 overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
            <FiInfo className="mr-2" /> Model Analysis
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FiX />
          </button>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
              <FiBox className="mr-2" /> Geometry
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-500 dark:text-gray-400">Vertices:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.vertices.toLocaleString()}</div>

              <div className="text-gray-500 dark:text-gray-400">Faces:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.faces.toLocaleString()}</div>

              <div className="text-gray-500 dark:text-gray-400">Triangles:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.triangles.toLocaleString()}</div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
              <FiLayers className="mr-2" /> Structure
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-500 dark:text-gray-400">Objects:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.objects}</div>

              <div className="text-gray-500 dark:text-gray-400">Meshes:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.meshes}</div>

              <div className="text-gray-500 dark:text-gray-400">Materials:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.materials}</div>

              <div className="text-gray-500 dark:text-gray-400">Textures:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.textures}</div>

              <div className="text-gray-500 dark:text-gray-400">Animations:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.animations}</div>

              <div className="text-gray-500 dark:text-gray-400">Bones:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.bones}</div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
              <FiImage className="mr-2" /> Dimensions
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-500 dark:text-gray-400">Width:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.size.width} units</div>

              <div className="text-gray-500 dark:text-gray-400">Height:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.size.height} units</div>

              <div className="text-gray-500 dark:text-gray-400">Depth:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.size.depth} units</div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
              <FiCpu className="mr-2" /> Performance
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-500 dark:text-gray-400">Memory Usage:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">{stats.memoryUsage} MB</div>

              <div className="text-gray-500 dark:text-gray-400">Complexity:</div>
              <div className="text-gray-900 dark:text-white font-medium text-right">
                {stats.triangles > 1000000 ? 'High' : stats.triangles > 100000 ? 'Medium' : 'Low'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Html>
  );
};

ModelAnalyzer.propTypes = {
  modelRef: PropTypes.object.isRequired,
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired
};

export default ModelAnalyzer;
