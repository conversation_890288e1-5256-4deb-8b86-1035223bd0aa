import React from 'react';
import PropTypes from 'prop-types';
import { motion } from 'framer-motion';

/**
 * PageTransition component for adding smooth transitions between pages
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to be wrapped
 * @param {string|Object} props.variant - Animation variant name or custom variants object
 * @param {string|Object} props.transitionType - Transition type name or custom transition settings
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.style - Additional inline styles
 * @param {boolean} props.layoutId - Enable layout animations with a unique ID
 * @returns {React.ReactElement} The PageTransition component
 */
const ComponentName = memo((props) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

PageTransition.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  transitionType: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  className: PropTypes.string,
  style: PropTypes.object,
  layoutId: PropTypes.string
};

export default PageTransition;
