import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  FiCpu, // Use FiCpu instead of FiBrain
  FiGrid,
  FiLayers,
  FiZap,
  FiX,
  FiArrowRight,
  FiStar,
  FiEye,
  FiHeart
} from 'react-icons/fi';

const ComponentName = memo((props) => {
  const [isVisible, setIsVisible] = useState(true);
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      icon: <FiCpu className="h-8 w-8" />,
      title: 'Trợ Lý AI Mô Hình',
      description: 'Phân tích thông minh và gợi ý cải thiện mô hình 3D với công nghệ AI tiên tiến',
      color: 'purple',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: <FiGrid className="h-8 w-8" />,
      title: 'Phòng Trưng Bày Ảo',
      description: 'Tr<PERSON><PERSON> nghiệm 3D nhập vai với hỗ trợ VR/AR và không gian tương tác',
      color: 'blue',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: <FiLayers className="h-8 w-8" />,
      title: 'Bộ Sưu Tập Thông Minh',
      description: 'AI tự động tuyển chọn và gợi ý mô hình phù hợp với sở thích của bạn',
      color: 'green',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      icon: <FiZap className="h-8 w-8" />,
      title: 'Studio Thiết Kế Tương Tác',
      description: 'Không gian làm việc 3D collaborative với chỉnh sửa real-time',
      color: 'orange',
      gradient: 'from-orange-500 to-red-500'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [features.length]);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -100 }}
        className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white overflow-hidden"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12"></div>
        </div>

        {/* Content */}
        <div className="relative container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            {/* Left side - Feature showcase */}
            <div className="flex-1 flex items-center space-x-6">
              {/* New badge */}
              <motion.div
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2"
              >
                <FiStar className="h-5 w-5 text-yellow-300" />
                <span className="font-bold text-sm">MỚI</span>
              </motion.div>

              {/* Feature content */}
              <div className="flex-1">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentFeature}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.5 }}
                    className="flex items-center space-x-4"
                  >
                    <div className={`p-3 bg-gradient-to-r ${features[currentFeature].gradient} rounded-lg`}>
                      {features[currentFeature].icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">
                        {features[currentFeature].title}
                      </h3>
                      <p className="text-sm opacity-90 max-w-md">
                        {features[currentFeature].description}
                      </p>
                    </div>
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            {/* Right side - Actions */}
            <div className="flex items-center space-x-4">
              {/* Feature indicators */}
              <div className="hidden md:flex space-x-2">
                {features.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentFeature(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentFeature
                        ? 'bg-white scale-125'
                        : 'bg-white/50 hover:bg-white/75'
                    }`}
                  />
                ))}
              </div>

              {/* CTA Button */}
              <Link
                to="/features-demo"
                className="flex items-center space-x-2 bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors group"
              >
                <span>Xem Demo</span>
                <FiArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>

              {/* Close button */}
              <button
                onClick={() => setIsVisible(false)}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                aria-label="Đóng thông báo"
              >
                <FiX className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Mobile layout */}
          <div className="md:hidden mt-4">
            <div className="flex justify-center space-x-2 mb-4">
              {features.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentFeature(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentFeature
                      ? 'bg-white scale-125'
                      : 'bg-white/50'
                  }`}
                />
              ))}
            </div>

            <div className="text-center">
              <Link
                to="/features-demo"
                className="inline-flex items-center space-x-2 bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                <span>Khám Phá Tính Năng Mới</span>
                <FiArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom highlight */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400"></div>
      </motion.div>
    </AnimatePresence>
  );
};

// Compact version for header
export const CompactFeaturesAnnouncement = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className="bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center py-2 text-sm"
    >
      <div className="container mx-auto px-4 flex items-center justify-center space-x-4">
        <div className="flex items-center space-x-2">
          <FiStar className="h-4 w-4 text-yellow-300" />
          <span className="font-medium">Tính năng mới:</span>
          <span>Trợ lý AI, Phòng trưng bày ảo, Bộ sưu tập thông minh</span>
        </div>

        <Link
          to="/features-demo"
          className="flex items-center space-x-1 bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full transition-colors"
        >
          <span>Xem demo</span>
          <FiArrowRight className="h-3 w-3" />
        </Link>

        <button
          onClick={() => setIsVisible(false)}
          className="p-1 hover:bg-white/20 rounded"
        >
          <FiX className="h-4 w-4" />
        </button>
      </div>
    </motion.div>
  );
};

// Feature highlight cards for homepage
export const FeatureHighlights = () => {
  const highlights = [
    {
      icon: <FiCpu className="h-6 w-6" />,
      title: 'AI Phân Tích',
      description: 'Đánh giá chất lượng mô hình tự động',
      color: 'purple',
      stats: '95% độ chính xác'
    },
    {
      icon: <FiGrid className="h-6 w-6" />,
      title: 'VR/AR Ready',
      description: 'Trải nghiệm nhập vai hoàn toàn',
      color: 'blue',
      stats: 'Hỗ trợ WebXR'
    },
    {
      icon: <FiLayers className="h-6 w-6" />,
      title: 'Smart Curation',
      description: 'AI tự động tuyển chọn nội dung',
      color: 'green',
      stats: '10K+ mô hình'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {highlights.map((highlight, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group"
        >
          <div className={`w-12 h-12 bg-${highlight.color}-100 dark:bg-${highlight.color}-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
            <div className={`text-${highlight.color}-600 dark:text-${highlight.color}-400`}>
              {highlight.icon}
            </div>
          </div>

          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {highlight.title}
          </h3>

          <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
            {highlight.description}
          </p>

          <div className={`text-${highlight.color}-600 dark:text-${highlight.color}-400 text-sm font-medium`}>
            {highlight.stats}
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default NewFeaturesAnnouncement;
