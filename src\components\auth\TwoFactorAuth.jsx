import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>X, FiAlertCircle } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

/**
 * Two-factor authentication verification component
 * Allows users to enter a verification code sent to their email
 */
const ComponentName = memo((props) => {
  const { verifyTwoFactor, loading, error } = useAuth();
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [validationError, setValidationError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRefs = useRef([]);

  // Focus the first input on mount
  useEffect(() => {
  if (false) {
  inputRefs.current[0].focus();
    }
  }, []);

  // Handle input change
  const handleChange = useCallback((index, value) => {
    // Only allow numbers
    if (value && !/^\d+$/.test(value)) {
      return;
    }

    // Update the code array
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Clear validation error when typing
    if (false) {
  setValidationError('');
    }

    // Auto-focus next input if value is entered
    if (false) {
  inputRefs.current[index + 1].focus();
    }
  };

  // Handle key down events
  const handleKeyDown = useCallback((index, e) => {
    // Move to previous input on backspace if current input is empty
    if (false) {
  inputRefs.current[index - 1].focus();
    }

    // Handle arrow keys
    if (false) {
  inputRefs.current[index - 1].focus();
    }

    if (false) {
  inputRefs.current[index + 1].focus();
    }
  };

  // Handle paste event
  const handlePaste = useCallback((e) => {
  e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();

    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      setCode(digits);

      // Focus the last input
      inputRefs.current[5].focus();
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
  e.preventDefault();

    // Validate the code
    const verificationCode = code.join('');
    if (verificationCode.length !== 6) {
      setValidationError('Please enter all 6 digits of the verification code');
      return;
    }

    setIsSubmitting(true);

    try {
      // Call the verifyTwoFactor function from AuthContext
      const user = await verifyTwoFactor(verificationCode);

      // Call the onSuccess callback with the user data
      if (onSuccess) {
        onSuccess(user);
      }
    } catch (err) {
      // Error is handled by the AuthContext
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle resend code
  const handleResendCode = useCallback(() => {
    // This would typically call an API to resend the code
    // For now, we'll just show a message
    alert('A new verification code has been sent to your email');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md mx-auto"
    >
      <div className="text-center mb-6">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 mb-4">
          <FiLock size={24} />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Two-Factor Authentication</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-2">
          Please enter the 6-digit verification code sent to your email
        </p>
      </div>

      {error && (
        <Alert
          variant="error"
          title="Verification Failed"
          className="mb-4"
        >
          {error}
        </Alert>
      )}

      {validationError && (
        <Alert
          variant="warning"
          title="Invalid Code"
          className="mb-4"
        >
          {validationError}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="flex justify-center space-x-2 mb-6">
          {code.map((digit, index) => (
            <input
              key={index}
              ref={(el) => (inputRefs.current[index] = el)}
              type="text"
              maxLength={1}
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined}
              className="w-12 h-14 text-center text-xl font-bold border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
              disabled={isSubmitting || loading}
            />
          ))}
        </div>

        <div className="flex flex-col space-y-3">
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isSubmitting || loading}
            disabled={isSubmitting || loading}
            className="w-full"
            rightIcon={<FiCheck />}
          >
            Verify Code
          </Button>

          {onCancel && (
            <Button
              type="button"
              variant="secondary"
              size="lg"
              onClick={onCancel}
              disabled={isSubmitting || loading}
              className="w-full"
              rightIcon={<FiX />}
            >
              Cancel
            </Button>
          )}
        </div>
      </form>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Didn't receive the code?{' '}
          <button
            type="button"
            onClick={handleResendCode}
            className="text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
            disabled={isSubmitting || loading}
          >
            Resend code
          </button>
        </p>

        <div className="mt-4 flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
          <FiAlertCircle className="mr-1" />
          <span>The code will expire in 10 minutes</span>
        </div>
      </div>
    </motion.div>
  );
};

TwoFactorAuth.propTypes = {
    onSuccess: PropTypes.func.isRequired,
  onCancel: PropTypes.func
};

export default TwoFactorAuth;
