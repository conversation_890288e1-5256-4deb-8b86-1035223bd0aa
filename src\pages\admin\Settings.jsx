import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiSave, FiRefreshCw, FiSettings, FiDatabase, FiMail, FiShield,
  FiGlobe, FiUpload, FiDollarSign, FiUsers, FiToggleLeft, FiToggleRight
} from 'react-icons/fi';

const Settings = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
    siteName: '3DSKETCHUP.NET',
      siteDescription: 'Premium 3D Models Repository',
      siteUrl: 'https://3dsketchup.net',
      adminEmail: '<EMAIL>',
      timezone: 'UTC',
      language: 'en',
      maintenanceMode: false
    },
    upload: {
    maxFileSize: 100, // MB
      allowedFormats: ['skp', 'obj', 'fbx', '3ds', 'dae'],
      requireApproval: true,
      autoGenerateThumbnails: true,
      compressionEnabled: true,
      virusScanEnabled: true
    },
    payment: {
    currency: 'USD',
      taxRate: 0,
      freeDownloadsPerMonth: 5,
      basicPlanPrice: 9.99,
      premiumPlanPrice: 19.99,
      professionalPlanPrice: 39.99,
      stripeEnabled: false,
      paypalEnabled: false
    },
    email: {
    smtpHost: '',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: '3DSKETCHUP.NET',
      emailVerificationRequired: true,
      welcomeEmailEnabled: true
    },
    security: {
    passwordMinLength: 8,
      requireSpecialChars: true,
      sessionTimeout: 24, // hours
      maxLoginAttempts: 5,
      twoFactorEnabled: false,
      ipWhitelistEnabled: false,
      ipWhitelist: []
    },
    api: {
    rateLimit: 1000, // requests per hour
      apiKeyRequired: false,
      corsEnabled: true,
      allowedOrigins: ['*'],
      webhooksEnabled: false,
      webhookUrl: ''
    }
  });

  const tabs = [
    { id: 'general', name: 'General', icon: <FiSettings className="h-5 w-5" /> },
    { id: 'upload', name: 'Upload', icon: <FiUpload className="h-5 w-5" /> },
    { id: 'payment', name: 'Payment', icon: <FiDollarSign className="h-5 w-5" /> },
    { id: 'email', name: 'Email', icon: <FiMail className="h-5 w-5" /> },
    { id: 'security', name: 'Security', icon: <FiShield className="h-5 w-5" /> },
    { id: 'api', name: 'API', icon: <FiDatabase className="h-5 w-5" /> }
  ];

  useEffect(() => {
  loadSettings();
  }, []);

  const loadSettings = async () => {
  try {
      setLoading(true);
      // In a real app, you would fetch settings from the API
      // const response = await api.get('/admin/settings');
      // setSettings(response.data);

      // For now, we'll use the default settings
      } catch (error) {
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
  try {
      setLoading(true);

      // In a real app, you would save settings to the API
      // await api.put('/admin/settings', settings);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Settings saved successfully');
    } catch (error) {
      toast.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (section, field, value) => {
  setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleToggle = (section, field) => {
  setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: !prev[section][field]
      }
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Site Name
        </label>
        <input
          type="text"
          value={settings.general.siteName}
          onChange={(e) => handleInputChange('general', 'siteName', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Site Description
        </label>
        <textarea
          value={settings.general.siteDescription}
          onChange={(e) => handleInputChange('general', 'siteDescription', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Site URL
        </label>
        <input
          type="url"
          value={settings.general.siteUrl}
          onChange={(e) => handleInputChange('general', 'siteUrl', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Admin Email
        </label>
        <input
          type="email"
          value={settings.general.adminEmail}
          onChange={(e) => handleInputChange('general', 'adminEmail', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div className="flex items-center justify-between">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Maintenance Mode
          </label>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Enable to put the site in maintenance mode
          </p>
        </div>
        <button
          onClick={() => handleToggle('general', 'maintenanceMode')}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.general.maintenanceMode ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.general.maintenanceMode ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
    </div>
  );

  const renderUploadSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Max File Size (MB)
        </label>
        <input
          type="number"
          value={settings.upload.maxFileSize}
          onChange={(e) => handleInputChange('upload', 'maxFileSize', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Allowed Formats
        </label>
        <input
          type="text"
          value={settings.upload.allowedFormats.join(', ')}
          onChange={(e) => handleInputChange('upload', 'allowedFormats', e.target.value.split(', '))}
          placeholder="skp, obj, fbx, 3ds, dae"
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div className="space-y-4">
        {[
          { key: 'requireApproval', label: 'Require Approval', desc: 'New uploads need admin approval' },
          { key: 'autoGenerateThumbnails', label: 'Auto Generate Thumbnails', desc: 'Automatically create thumbnails' },
          { key: 'compressionEnabled', label: 'Compression', desc: 'Compress uploaded files' },
          { key: 'virusScanEnabled', label: 'Virus Scan', desc: 'Scan uploads for viruses' }
        ].map(setting => (
          <div key={setting.key} className="flex items-center justify-between">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {setting.label}
              </label>
              <p className="text-sm text-gray-500 dark:text-gray-400">{setting.desc}</p>
            </div>
            <button
              onClick={() => handleToggle('upload', setting.key)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                settings.upload[setting.key] ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  settings.upload[setting.key] ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPaymentSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Currency
        </label>
        <select
          value={settings.payment.currency}
          onChange={(e) => handleInputChange('payment', 'currency', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        >
          <option value="USD">USD - US Dollar</option>
          <option value="EUR">EUR - Euro</option>
          <option value="GBP">GBP - British Pound</option>
          <option value="VND">VND - Vietnamese Dong</option>
        </select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Basic Plan Price
          </label>
          <input
            type="number"
            step="0.01"
            value={settings.payment.basicPlanPrice}
            onChange={(e) => handleInputChange('payment', 'basicPlanPrice', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Premium Plan Price
          </label>
          <input
            type="number"
            step="0.01"
            value={settings.payment.premiumPlanPrice}
            onChange={(e) => handleInputChange('payment', 'premiumPlanPrice', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Free Downloads Per Month
        </label>
        <input
          type="number"
          value={settings.payment.freeDownloadsPerMonth}
          onChange={(e) => handleInputChange('payment', 'freeDownloadsPerMonth', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'upload':
        return renderUploadSettings();
      case 'payment':
        return renderPaymentSettings();
      case 'email':
        return <div className="text-gray-500 dark:text-gray-400">Email settings coming soon...</div>;
      case 'security':
        return <div className="text-gray-500 dark:text-gray-400">Security settings coming soon...</div>;
      case 'api':
        return <div className="text-gray-500 dark:text-gray-400">API settings coming soon...</div>;
      default:
        return null;
    }
  };

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">Configure your application settings.</p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={loadSettings}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Reload
          </button>

          <button
            onClick={saveSettings}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center disabled:opacity-50"
          >
            <FiSave className="h-4 w-4 mr-2" />
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
    activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                {tab.icon}
                <span className="ml-2">{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {renderTabContent()}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
