import React, { useState, useEffect, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiClock, FiDownload, FiEye, FiStar, FiArrowRight } from 'react-icons/fi';
import realDataService from '../services/realDataService';
import ImageWithFallback from './ui/ImageWithFallback';
import LoadingIndicator from './ui/LoadingIndicator';

const ComponentName = memo((props) => {
  // Use passed models or fetch if not provided
  const [recentModels, setRecentModels] = useState(models);
  const [loading, setLoading] = useState(!models.length);
  const [error, setError] = useState(null);

  const fetchRecentModels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const fetchedModels = await realDataService.getRecentModels();

      // Limit the results
      const limitedModels = fetchedModels.slice(0, limit);

      setRecentModels(limitedModels);
    } catch (err) {
      setError('Failed to load recent models');
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    if (models.length > 0) {
      setRecentModels(models.slice(0, limit));
      setLoading(false);
    } else {
      fetchRecentModels();
    }
  }, [models, limit, fetchRecentModels]);

  const formatDate = (dateString) => {
  const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
    opacity: 1,
      transition: {
    staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
    y: 0,
      opacity: 1,
      transition: {
    type: 'spring',
        stiffness: 300,
        damping: 24
      }
    }
  };

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
        <LoadingIndicator size="md" text="Loading recent models..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center ${className}`}>
        <p className="text-red-500 dark:text-red-400 mb-4">{error}</p>
        <button
          onClick={fetchRecentModels}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden ${className}`}>
      {showHeader && (
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
              <FiClock className="mr-2 text-blue-600" />
              Recent Models
            </h2>
            <Link
              to="/models?sort=recent"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium flex items-center"
            >
              View All
              <FiArrowRight className="ml-1 w-4 h-4" />
            </Link>
          </div>
        </div>
      )}

      <motion.div
        className="p-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {recentModels.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentModels.map((model) => (
              <motion.div
                key={model._id || model.id}
                variants={itemVariants}
                className="group"
              >
                <Link
                  to={`/model/${model._id || model.id}`}
                  className="block bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300"
                >
                  <div className="aspect-video relative overflow-hidden">
                    <ImageWithFallback
                      src={model.imageUrl}
                      alt={model.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      lowResSrc="/images/placeholder-tiny.jpg"
                    />

                    {/* Premium badge */}
                    {model.isPremium && (
                      <div className="absolute top-2 right-2">
                        <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">
                          Premium
                        </span>
                      </div>
                    )}

                    {/* Date overlay */}
                    <div className="absolute bottom-2 left-2">
                      <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {formatDate(model.createdAt || model.uploadDate)}
                      </span>
                    </div>
                  </div>

                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {model.title}
                    </h3>

                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span className="flex items-center">
                        <FiStar className="w-3 h-3 mr-1 text-yellow-500" />
                        {model.rating || '4.5'}
                      </span>

                      <span className="flex items-center">
                        <FiDownload className="w-3 h-3 mr-1" />
                        {model.downloads || 0}
                      </span>

                      <span className="flex items-center">
                        <FiEye className="w-3 h-3 mr-1" />
                        {model.views || 0}
                      </span>
                    </div>

                    <div className="mt-2">
                      <span className="inline-block bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
                        {model.category}
                      </span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiClock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No recent models
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Check back later for new uploads
            </p>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default memo(RecentModels);
