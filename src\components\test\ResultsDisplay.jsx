import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiDownload, FiStar, FiTag, FiPackage, FiEye, FiTarget, FiLayers, FiGrid } from 'react-icons/fi';

const ComponentName = memo((props) => {
  const [activeTab, setActiveTab] = useState('all');
  if (!results || (type === 'models' && !results.models?.length) || (type === 'extensions' && !results.extensions?.length)) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400 mb-2">
          <FiPackage className="h-12 w-12 mx-auto" />
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          No {type} found for this image
        </p>
      </div>
    );
  }

  const items = type === 'models' ? results.models : results.extensions;
  const hasMatchLevels = results.matchLevels && type === 'models';

  // Get items based on active tab
  const getDisplayItems = () => {
    if (false) {
  return items;
    }
    return results.matchLevels[activeTab] || [];
  };

  const displayItems = getDisplayItems();

  return (
    <div className="space-y-6">
      {/* Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
          Visual Search Results Summary
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {items.length}
            </div>
            <div className="text-gray-600 dark:text-gray-400">
              {type === 'models' ? 'Models' : 'Extensions'} Found
            </div>
          </div>
          {hasMatchLevels && (
            <>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {results.matchLevels.exact?.length || 0}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Exact Matches</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {results.matchLevels.similar?.length || 0}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Similar</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {results.matchLevels.related?.length || 0}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Related</div>
              </div>
            </>
          )}
          {!hasMatchLevels && results.analysis && (
            <>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {results.analysis.elements?.length || 0}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Elements</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {results.analysis.objects?.length || 0}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Objects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {results.analysis.tags?.length || 0}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Tags</div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Match Level Tabs for Models */}
      {hasMatchLevels && (
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setActiveTab('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
    activeTab === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <FiGrid className="inline h-4 w-4 mr-1" />
            All ({items.length})
          </button>
          <button
            onClick={() => setActiveTab('exact')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
    activeTab === 'exact'
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <FiTarget className="inline h-4 w-4 mr-1" />
            Exact ({results.matchLevels.exact?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('similar')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
    activeTab === 'similar'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <FiLayers className="inline h-4 w-4 mr-1" />
            Similar ({results.matchLevels.similar?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('related')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
    activeTab === 'related'
                ? 'bg-orange-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <FiPackage className="inline h-4 w-4 mr-1" />
            Related ({results.matchLevels.related?.length || 0})
          </button>
        </div>
      )}

      {/* Enhanced Analysis Details */}
      {results.analysis && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              AI Analysis Details
            </h3>
            {results.analysis.confidence && (
              <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded text-xs">
                {Math.round(results.analysis.confidence * 100)}% confidence
              </span>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            {/* Architectural Elements */}
            {results.analysis.elements?.length > 0 && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Architectural Elements:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {results.analysis.elements.slice(0, 5).map((element, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs"
                    >
                      {element}
                    </span>
                  ))}
                  {results.analysis.elements.length > 5 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      +{results.analysis.elements.length - 5} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Furniture */}
            {results.analysis.furniture?.length > 0 && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Furniture:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {results.analysis.furniture.slice(0, 5).map((item, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded text-xs"
                    >
                      {item}
                    </span>
                  ))}
                  {results.analysis.furniture.length > 5 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      +{results.analysis.furniture.length - 5} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Decorative Items */}
            {results.analysis.decorativeItems?.length > 0 && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Decorative Items:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {results.analysis.decorativeItems.slice(0, 4).map((item, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-xs"
                    >
                      {item}
                    </span>
                  ))}
                  {results.analysis.decorativeItems.length > 4 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      +{results.analysis.decorativeItems.length - 4} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Styles */}
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Styles:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {results.analysis.architecturalStyle && (
                  <span className="px-2 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 rounded text-xs">
                    {results.analysis.architecturalStyle}
                  </span>
                )}
                {results.analysis.interiorStyle && (
                  <span className="px-2 py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300 rounded text-xs">
                    {results.analysis.interiorStyle}
                  </span>
                )}
                {results.analysis.style && results.analysis.style !== results.analysis.architecturalStyle && results.analysis.style !== results.analysis.interiorStyle && (
                  <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-xs">
                    {results.analysis.style}
                  </span>
                )}
              </div>
            </div>

            {/* Materials */}
            {(results.analysis.naturalMaterials?.length > 0 || results.analysis.manufacturedMaterials?.length > 0) && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Materials:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {results.analysis.naturalMaterials?.slice(0, 3).map((material, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 rounded text-xs"
                    >
                      {material}
                    </span>
                  ))}
                  {results.analysis.manufacturedMaterials?.slice(0, 3).map((material, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-slate-100 dark:bg-slate-900/30 text-slate-800 dark:text-slate-300 rounded text-xs"
                    >
                      {material}
                    </span>
                  ))}
                  {(results.analysis.naturalMaterials?.length + results.analysis.manufacturedMaterials?.length) > 6 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      +{(results.analysis.naturalMaterials?.length || 0) + (results.analysis.manufacturedMaterials?.length || 0) - 6} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Colors */}
            {results.analysis.colors?.length > 0 && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Colors:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {results.analysis.colors.slice(0, 6).map((color, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 rounded text-xs"
                    >
                      {color}
                    </span>
                  ))}
                  {results.analysis.colors.length > 6 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      +{results.analysis.colors.length - 6} more
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Room and Building Type */}
          {(results.analysis.roomType || results.analysis.buildingType) && (
            <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {results.analysis.roomType && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Room Type:</span>
                    <span className="ml-2 px-2 py-1 bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-300 rounded text-xs">
                      {results.analysis.roomType}
                    </span>
                  </div>
                )}
                {results.analysis.buildingType && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Building Type:</span>
                    <span className="ml-2 px-2 py-1 bg-teal-100 dark:bg-teal-900/30 text-teal-800 dark:text-teal-300 rounded text-xs">
                      {results.analysis.buildingType}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {displayItems.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow"
          >
            {/* Item Header */}
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-gray-900 dark:text-white text-sm leading-tight">
                  {type === 'models' ? item.title : item.name}
                </h4>
                <div className="flex flex-col gap-1 ml-2 flex-shrink-0">
                  {item.similarityScore && (
                    <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded text-center">
                      {Math.round(item.similarityScore * 100)}%
                    </span>
                  )}
                  {item.visualSimilarity && !item.similarityScore && (
                    <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded text-center">
                      {Math.round(item.visualSimilarity * 100)}%
                    </span>
                  )}
                </div>
              </div>

              <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {item.description}
              </p>

              {/* Stats */}
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <FiDownload className="h-3 w-3" />
                    <span>{item.downloads || 0}</span>
                  </div>
                  {item.rating && (
                    <div className="flex items-center gap-1">
                      <FiStar className="h-3 w-3 text-yellow-500" />
                      <span>{item.rating}</span>
                    </div>
                  )}
                  {type === 'models' && item.views && (
                    <div className="flex items-center gap-1">
                      <FiEye className="h-3 w-3" />
                      <span>{item.views}</span>
                    </div>
                  )}
                </div>
                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                  {item.category}
                </span>
              </div>

              {/* Tags */}
              {item.tags?.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {item.tags.slice(0, 3).map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="px-2 py-1 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs flex items-center gap-1"
                    >
                      <FiTag className="h-2 w-2" />
                      {tag}
                    </span>
                  ))}
                  {item.tags.length > 3 && (
                    <span className="px-2 py-1 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      +{item.tags.length - 3}
                    </span>
                  )}
                </div>
              )}

              {/* Model specific info */}
              {type === 'models' && (
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 dark:text-gray-400">
                    {item.fileSize && (
                      <div>
                        <span className="font-medium">Size:</span> {item.fileSize}MB
                      </div>
                    )}
                    {item.format && (
                      <div>
                        <span className="font-medium">Format:</span> {item.format}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Extension specific info */}
              {type === 'extensions' && (
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 dark:text-gray-400">
                    {item.developer && (
                      <div>
                        <span className="font-medium">Developer:</span> {item.developer}
                      </div>
                    )}
                    {item.version && (
                      <div>
                        <span className="font-medium">Version:</span> {item.version}
                      </div>
                    )}
                    {item.price && (
                      <div>
                        <span className="font-medium">Price:</span> {item.price}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Match Reasons for Models */}
              {type === 'models' && item.matchReasons?.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    <span className="font-medium">Match reasons:</span>
                    <div className="mt-1 space-y-1">
                      {item.matchReasons.slice(0, 2).map((reason, reasonIndex) => (
                        <div key={reasonIndex} className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                          {reason}
                        </div>
                      ))}
                      {item.matchReasons.length > 2 && (
                        <div className="text-xs text-gray-400">
                          +{item.matchReasons.length - 2} more reasons
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Load More Button */}
      {displayItems.length >= 6 && (
        <div className="text-center">
          <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Load More Results
          </button>
        </div>
      )}

      {/* No Results Message */}
      {displayItems.length === 0 && hasMatchLevels && activeTab !== 'all' && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <FiPackage className="h-8 w-8 mx-auto" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            No {activeTab} matches found for this image
          </p>
          <button
            onClick={() => setActiveTab('all')}
            className="mt-2 text-blue-600 dark:text-blue-400 hover:underline text-sm"
          >
            View all results
          </button>
        </div>
      )}
    </div>
  );
};

export default ResultsDisplay;
